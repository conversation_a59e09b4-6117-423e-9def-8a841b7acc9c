# pylint: skip-file
import argparse
import copy
import inspect
import json
import os
import string
import sys
from datetime import datetime, timedelta
from pathlib import PurePosixPath, PureWindowsPath
from time import sleep
from xml.etree import ElementTree as ET

import requests

# import dateutil
from dateutil import parser as dtparser
from dateutil import tz
from fuzzywuzzy import fuzz  # for name matching
from utils.LoggerService import logger_service

from ..token_service import CustomHttpAdapter


# SSL-configured helper functions for Reach API calls
def _create_ssl_session():
    """Create a requests session with SSL configuration for Reach Engine API"""
    session = requests.Session()
    session.mount("https://", CustomHttpAdapter())
    return session


def _make_reach_api_get(url, headers=None, params=None):
    """Make SSL-configured GET request to Reach API"""
    session = _create_ssl_session()
    return session.get(url=url, headers=headers, params=params, verify=False)


def _make_reach_api_post(url, headers=None, json=None, data=None):
    """Make SSL-configured POST request to Reach API"""
    session = _create_ssl_session()
    return session.post(url=url, headers=headers, json=json, data=data, verify=False)


def _make_reach_api_put(url, headers=None, json=None, data=None):
    """Make SSL-configured PUT request to Reach API"""
    session = _create_ssl_session()
    return session.put(url=url, headers=headers, json=json, data=data, verify=False)


def _make_reach_api_delete(url, headers=None):
    """Make SSL-configured DELETE request to Reach API"""
    session = _create_ssl_session()
    return session.delete(url=url, headers=headers, verify=False)


def config_parse(config):
    config_json = {
        "reach_engine_login": config.get("reach_engine_login"),
        "reach_engine_password": config.get("reach_engine_password"),
        "reach_prod_address": config.get("reach_prod_address"),
        "reach_staging_address": config.get("reach_staging_address"),
    }
    return config_json


def send_slack_msg(msg, env):
    """
    To send a Slack message.
    :param msg:
    :param env:
    :return:
    """

    # NOTE: this is a legacy webhook. One way communication to Slack. FUTURE: An app would provide better integration.
    hook_url = "https://hooks.slack.com/services/########/########/######REDACTED#########"  # DTCI RED Watcher dev channel

    # TODO: specify which channel for Prod
    if env == "prod":
        message = "{}".format(msg)
        slack_channel = "reach-fxng-workflow-notifications"
    else:
        message = "*DEV*  {}".format(msg)
        slack_channel = "reach-engine-dev-notifications"

    json_body = {"text": message, "channel": slack_channel}

    # logger_service.info('sending slack: {}'.format(jsonBody))
    resp = requests.post(hook_url, json=json_body)
    if resp.status_code == requests.codes.ok:
        # print('...Slack sent')
        logger_service.info("...Slack sent")
    else:
        print(
            "Unable to send to Slack. Returned error: {0} with message: {1}".format(
                resp.status_code, resp.headers
            )
        )
        sys.exit(1)


def radar_get_credits_given_product_id(radar_login, radar_pass, prod_id):
    """
    Get acting credits from RADAR. Note: might not be reliable if data was not entered in Radar.
    :param radar_login:
    :param radar_pass:
    :param prod_id:
    :return:
    """
    criteria_xml = """<criteria>
  <criteria_section name="security">
    <criteria_item name="uid" value="{0}" data_type="c" operator="=" />
    <criteria_item name="pwd" value="{1}" data_type="c" operator="=" />
    <criteria_item name="domain" value="swna" data_type="c" operator="=" />
    <criteria_item name="class_name" value="RadarCredit" data_type="c" operator="=" />
    <criteria_item name="webservice_name" value="getCreditDetail" data_type="c" operator="=" />
  </criteria_section>
  <criteria_section name="credit">
    <criteria_item name="product_id" data_type="n" value="{2}" format="" operator="="/>
  </criteria_section>
</criteria>""".format(
        radar_login, radar_pass, prod_id
    )

    radar_url = "http://radarweb.wds.disney.com/radarweb/services/RadarCredits.asmx/getCreditDetail"
    params = {"criteria_xml": criteria_xml}
    resp = requests.get(radar_url, params=params)
    etree = ET.fromstring(resp.content)
    diffgr = "{urn:schemas-microsoft-com:xml-diffgram-v1}"
    cast_list = etree.findall(
        '{0}diffgram/credit/radarProduct/radarCredit[valid_credit_subtype="CAST"]'.format(
            diffgr
        )
    )
    vocal_list = etree.findall(
        '{0}diffgram/credit/radarProduct/radarCredit[valid_credit_subtype="VOCAL TALENT"]'.format(
            diffgr
        )
    )
    collab_list = []
    # live action cast:
    for this_cast in cast_list:
        actor_name = this_cast.find("credit_name").text
        character_name = this_cast.find("addl_credit_info").text
        collab_list.append({"actor_name": actor_name, "character_name": character_name})

    # vocal cast (animation):
    for this_cast in vocal_list:
        # actor_name = this_cast.find('credit_name').text
        character_name = this_cast.find("addl_credit_info").text
        collab_list.append({"actor_name": "NONE", "character_name": character_name})

    return collab_list


def reach_login(u, p, fqdn):
    # log in to RED

    jsonBody = {"auth_user": u, "auth_password": p}
    # url = ''.join([httpType, self.ipaddr, '/reachengine/api/security/users/login'])
    # url = ''.join([ipArg, '/reachengine/security/login'])  # Reach 7
    url = "".join([fqdn, "/reachengine/api/security/users/login"])  # Reach 8
    print(url)
    # print(jsonBody)
    # resp = requests.post(url, params=jsonBody)  # Reach 7
    resp = requests.post(url, data=jsonBody)  # Reach 8
    print("Reach Login - Status code = {}".format(resp.status_code))
    if resp.status_code == requests.codes.ok:
        # print resp.cookies
        print(("Login to Reach successful at {}").format(fqdn))
        # return resp.cookies  # Reach 7
        return {"Authorization": resp.headers["Authorization"]}
    else:
        raise Exception("Unable to authenticate with Reach Engine")


def convert_windows_real_filepath_to_virtual_filepath(filepath):
    """
    Convert something like C:\\path\\to\\virtualfilesystem\\file to /virtualfilesystem/file
    :param filepath:
    :return:
    """
    # curr_sys = platform.system()
    # if 'windows' in curr_sys.lower():
    #     is_win = True
    # elif 'linux' in curr_sys.lower() or 'darwin' in curr_sys.lower():
    #     is_win = False
    # else:
    #     logger_service.error('Unable to determine platform. Value returned is {}'.format(curr_sys))
    #     return ''

    # if is_win:
    # parse path
    parsed_path = PureWindowsPath(filepath)
    parts_list = list(parsed_path.parts)
    drive_letter = parts_list[0]
    clean_posix_path = None
    if "b:" in drive_letter.lower():
        clean_parts = ["/Volumes", "Buffer"] + parts_list[1:]

    if "t:" in drive_letter.lower():
        clean_parts = ["/Volumes", "Tron"] + parts_list[1:]

    if "z:" in drive_letter.lower():
        clean_parts = ["/Volumes", "DataDrive"] + parts_list[1:]

    if (
        ("z:" not in drive_letter.lower())
        and ("t:" not in drive_letter.lower())
        and ("b:" not in drive_letter.lower())
    ):
        logger_service.error(
            'could not parse filepath to virtual filepath (unknown drive letter?). Given path "{}"'.format(
                filepath
            )
        )
        return ""

    clean_posix_path = PurePosixPath("/".join(clean_parts))
    clean_posix_path_str = clean_posix_path.as_posix()
    # else:

    if clean_posix_path is None:
        logger_service.error(
            'could not parse filepath to virtual filepath. Given path "{}"'.format(
                filepath
            )
        )
        return ""

    # could lookup VFS using https://staging8.reachengine.disney.com/reachengine/api/filesystem/vfs
    # but lazy and know the response will be:
    vfs = {
        "path": "/",
        "roots": [{"alias": "ReachEngine/", "path": "/Volumes/Buffer/ReachEngine/"}],
        "files": [],
    }

    filepath_vfs = ""
    for this_vfs in vfs.get("roots", []):
        this_vfs_path = this_vfs.get("path", "")
        this_vfs_alias = this_vfs.get("alias", "")
        if this_vfs_path in clean_posix_path_str:
            filepath_vfs = clean_posix_path_str.replace(this_vfs_path, this_vfs_alias)
            break

    return filepath_vfs


def convert_linux_real_filepath_to_virtual_filepath(filepath):
    """
    Convert something like
    /Volumes/Tron/ReachWork/Production/upload/1728956844073/file.jpg
    to
    temp/upload/1728956844073/file.jpg

    Just to a simple check, then replace.

    :param filepath:
    :return:
    """

    if not filepath.startswith(
        "/Volumes/Tron/ReachWork/Production"
    ) and not filepath.startswith("/Volumes/DataDrive/ReachWork/Production"):
        logger_service.error(
            'could not parse filepath to virtual filepath. Given path "{}"'.format(
                filepath
            )
        )
        return ""
    else:
        filepath = filepath.replace("/Volumes/Tron/ReachWork/Production/", "temp/")
        filepath = filepath.replace("/Volumes/DataDrive/ReachWork/Production/", "temp/")

    return filepath


def reach_search_for_series_or_special_or_movie(
    content_name, content_type, network, reach_token, reach_fqdn
):
    """
    Search in Reach for a series or special - try to see if it exists
    :param network:
    :param reach_fqdn:
    :param reach_token:
    :param content_name: "Name of Series or Special or Movie"
    :param content_type: "Series" or "Movie/Special"
    :return:
    """
    possible_matches = []
    exact_matches = []
    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    try:
        url = "{}/reachengine/api/abc/contents".format(reach_fqdn)
        response = _make_reach_api_get(
            url=url,
            headers=reach_token,
            params={
                "fetchLimit": "-1",
                "searchString": content_name,
                "type": content_type,
            },
        )
    except requests.exceptions.RequestException:
        # print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)
        # print('RED_get_all_partners: Response HTTP Status Code: {status_code}'.format(
        #    status_code=response.status_code))

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    # see if name matches closely. If so, see if network matches
    if response.status_code == requests.codes.ok:
        response_json = response.json()
        # print(json.dumps(response_json, indent=2))
        for this_content in response_json:
            this_content_value = this_content.get("value")
            this_content_network = this_content.get("network", {}).get("value")

            # see if this found item closely matches the content we're looking for
            match_ratio = fuzz.ratio(this_content_value.lower(), content_name.lower())
            if match_ratio > 95:
                if this_content_network.lower() == network.lower():
                    # return this_content
                    possible_matches.append(this_content)

            # now see if anything is 100 match and ignore others
            # (justification: 'Spider-Man' is 100%, but 'Spider-Man 2' is 91%
            # Spider-Man 2 is above the 90% threshold but if we have an exact match, let's use it)
            if match_ratio == 100:
                if this_content_network.lower() == network.lower():
                    exact_matches.append(this_content)

    if len(exact_matches) > 0:
        return exact_matches
    else:
        return possible_matches


def reach_search_for_collaborators_in_reach(actor_name, reach_token, reach_fqdn):
    """
    Search in Reach for a series or special - try to see if it exists
    :param actor_name:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """
    possible_matches = []
    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    try:
        url = "{}/reachengine/api/abc/collaborators".format(reach_fqdn)
        response = requests.get(
            url=url,
            headers=reach_token,
            params={"fetchLimit": "-1", "fetchOffset": "0", "searchString": actor_name},
        )
    except requests.exceptions.RequestException:
        # print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)
        # print('RED_get_all_partners: Response HTTP Status Code: {status_code}'.format(
        #    status_code=response.status_code))

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        # print(json.dumps(response_json, indent=2))
        if len(response_json) > 1:
            logger_service.info(
                'More than one collaborator found for "{}"'.format(actor_name)
            )

        return response_json
    else:
        return []


def reach_search_for_existing_package(content_json, reach_token, reach_fqdn):
    """
    Search in Reach for an existing package
    :param content_json:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """
    possible_matches = []
    this_func = inspect.currentframe().f_code.co_name  # for logging info later

    temp_content_type_raw = content_json.get("program_type", {}).get(
        "value", ""
    )  # either "Series" or "Movie/Special"
    # TODO: is this true? is acquired == series?
    if temp_content_type_raw == "series":
        temp_content_type = "Series"
    elif temp_content_type_raw == "movie" or temp_content_type_raw == "special":
        temp_content_type = "Movie/Special"
    elif temp_content_type_raw == "acquired":
        temp_content_type = "Series"
    else:
        logger_service.info("Unable to determine program type from JSON. Stopping.")
        return []
    temp_network_name = content_json.get("network", {}).get(
        "value", ""
    )  # network of the series/movie
    temp_content_name = content_json.get("content_name", {}).get(
        "value", ""
    )  # name of the series/movie
    temp_tms = content_json.get("episode_tms_id", {}).get("value", "")  # the TMS ID
    temp_matid = content_json.get("material_id", {}).get("value", "")  # the Material ID
    new_pkg_category = content_json.get("episode_category", {}).get("value", "")

    if "episode" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_Clean"
        category_search = "200"
    elif "episode c type" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_C3"
        category_search = "1740"
    elif "episode d type" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_D4"
        category_search = "1741"

    elif "movie (theatrical formatted for tv)" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_Movie"
        category_search = "1600"
    elif "movie c type" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_MovieC3"
        category_search = "1660"
    elif "movie d type" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_MovieD4"
        category_search = "1940"

    elif "special" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_Special"
        category_search = "342"
    elif "special c type" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_SpecialC3"
        category_search = "1742"
    elif "special d type" == new_pkg_category.lower():
        material_id_and_type = temp_matid + "_SpecialD4"
        category_search = "1743"
    else:
        return "error"  # no valid Episode type

    keywords_to_search = ""
    packageProperties = [""]
    if temp_tms.strip() != "":  # TMS is present - let's try this
        keywords_to_search = temp_tms
        packageProperties = ["tMSId"]
    else:  # no TMS - search for the Material ID in the nickname
        # TODO - replace this with search of External ID. Yes you can do this - use ['externalId']
        #  The problem is search results don't have "externalId" value.
        #  So you would have to get full package metadata to compare and validate.
        keywords_to_search = temp_matid
        packageProperties = ["name"]

    # get network id
    temp_network_data = reach_get_network_data_given_name(
        temp_network_name, reach_token, reach_fqdn
    )
    temp_network_id = temp_network_data.get("id", "")

    # get content id
    temp_content_data = reach_search_for_series_or_special_or_movie(
        content_json["content_name"]["value"],
        temp_content_type,
        content_json["network"]["value"],
        reach_token,
        reach_address,
    )

    if len(temp_content_data) == 1:
        temp_content_id = temp_content_data[0].get("id")

    if len(temp_content_data) > 1:
        logger_service.error(
            'Found {0} entries matching {1} for "{2}"'.format(
                len(temp_content_data),
                temp_content_type,
                content_json["content_name"]["value"],
            )
        )
        return "error"

    try:
        url = "{}/reachengine/api/abc/packages/searches".format(reach_fqdn)
        json = {
            "fetchOffset": 0,
            "fetchLimit": -1,
            "includeHidden": False,
            "keywords": keywords_to_search,
            "packageProperties": packageProperties,
            "contentType": temp_content_type,  # Series or  Movie/Special
            "network": [temp_network_id],
            "content": [temp_content_id],
            "category": [category_search],
        }
        response = requests.post(
            url=url,
            headers=reach_token,
            params={"fetchLimit": "-1", "fetchOffset": "0", "includeHidden": False},
            json=json,
        )
    except requests.exceptions.RequestException:
        # print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)
        # print('RED_get_all_partners: Response HTTP Status Code: {status_code}'.format(
        #    status_code=response.status_code))

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        # print(json.dumps(response_json, indent=2))
        # TODO: Note to self: here is where I would compare External IDs. But External ID is only visible in
        #  metadata and External Ingests. And since Reach API is so slow, it would take 16 seconds per package.
        # #  So let's do just 2 packages. Any more and we just error out
        # if len(response_json) < 3:
        #     for this_response in response_json:

        # if len(response_json) == 1or len(response_json) == 0:
        if len(response_json) == 0:
            return []
        elif len(response_json) == 1:
            # make sure pkg matches traffic code
            found_pkg = response_json[0]
            found_pkg_id = found_pkg.get("id", "")
            found_pkg_traffic_code = reach_get_traffic_code_given_package_id(
                found_pkg_id, reach_token, reach_address
            )
            if found_pkg_traffic_code != "" and found_pkg_traffic_code == temp_matid:
                return [
                    found_pkg
                ]  # the single pkg we found matches traffic code - return this pkg
            else:
                return (
                    []
                )  # the single pkg we found does not match traffic code - so results are empty

        elif len(response_json) > 1:
            for found_pkg in response_json:
                found_pkg_id = found_pkg.get("id", "")
                found_pkg_traffic_code = reach_get_traffic_code_given_package_id(
                    found_pkg_id, reach_token, reach_address
                )
                if (
                    found_pkg_traffic_code != ""
                    and found_pkg_traffic_code == temp_matid
                ):
                    return [found_pkg]  # return as a json in a single-item list

            # if we got here, we have not found a pkg with matching traffic code
            if temp_tms.strip() != "":
                logger_service.info(
                    'More than one package found with TMS ID "{0}" (type: "{1}", name: "{2}")'.format(
                        temp_tms, temp_content_type, temp_content_name
                    )
                )
            else:
                logger_service.info(
                    'More than one package found with Material ID "{0}" (type: "{1}", name: "{2}")'.format(
                        temp_tms, temp_content_type, temp_content_name
                    )
                )
            return "error"
    else:
        return "error"


# def reach_get_artwork_given_content_id(content_id, reach_token, reach_fqdn):
#     """
#     :param content_id:
#     :param reach_fqdn:
#     :param reach_token:
#     :return:
#     """
#     possible_matches = []
#     try:
#         url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
#         response = requests.get(
#             url=url,
#             headers=reach_token,
#             params={
#                 "view": "EXTENDED"
#             }
#         )
#     except requests.exceptions.RequestException:
#         print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
#         err_msg = ("Error calling Reach API ({})".format(url))
#         logger_service.error(err_msg)
#         raise ConnectionError(err_msg)
#
#     if response.status_code != requests.codes.ok:
#         err_msg = "Bad response from Reach API call '{}' : HTTP {}".format(url, response.status_code)
#         logger_service.error(err_msg)
#         raise Exception(err_msg)
#
#     if response.status_code == requests.codes.ok:
#         response_json = response.json()
#         artwork = response_json.get('artwork')
#         return artwork
#
#     return []


def reach_get_content(content_id, reach_token, reach_fqdn):
    """
    Search in Reach for a series or special - try to see if it exists
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.get(
            url=url,
            headers=reach_token,
        )
    except requests.exceptions.RequestException:
        # print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)
        # print('RED_get_all_partners: Response HTTP Status Code: {status_code}'.format(
        #    status_code=response.status_code))

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json
    else:
        return []


def reach_get_content_minimal(content_id, reach_token, reach_fqdn):
    """
    Get content (series or movie) _minimal_ data
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name  # for logging info later

    # get type of this series
    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = _make_reach_api_get(
            url=url,
            headers=reach_token,
        )
    except requests.exceptions.RequestException:
        # print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        content_full_json = response.json()
        content_full_type = content_full_json.get(
            "type"
        )  # should return either "Series" or "Movie/Special"

    # then get the minimal for this series

    try:
        url = "{0}/reachengine/api/abc/contents".format(reach_fqdn)
        params = {
            "fetchLimit": "-1",
            "fetchOffset": "0",
            "type": content_full_type,
            "view": "MINIMAL",
        }
        response = _make_reach_api_get(
            url=url,
            headers=reach_token,
            params=params,
        )
    except requests.exceptions.RequestException:
        # print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)
        # print('RED_get_all_partners: Response HTTP Status Code: {status_code}'.format(
        #    status_code=response.status_code))

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        content_minimal_list = response.json()

    content_min_json = {}
    for this_content_min in content_minimal_list:
        content_min_id = this_content_min.get("id")
        if content_min_id == content_id:
            content_min_json = this_content_min
            break

    if content_min_json == {}:
        raise Exception("Can't locate content id {}. Is it hidden?".format(content_id))

    return content_min_json


def reach_get_collaborators_in_content(content_id, reach_token, reach_fqdn):
    """
    Get actors from a series/movie
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name  # for logging info later
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/actors".format(
            reach_fqdn, content_id
        )
        response = requests.get(
            url=url,
            headers=reach_token,
            params={"fetchLimit": "-1", "fetchOffset": "0"},
        )
    except requests.exceptions.RequestException:
        # print('HTTP Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)
        # print('RED_get_all_partners: Response HTTP Status Code: {status_code}'.format(
        #    status_code=response.status_code))

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json
    else:
        return []


def reach_get_network_data_given_name(network_name, reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :param network_name:
    :return:
    """
    possible_matches = []
    try:
        url = "{}/reachengine/api/abc/networks".format(reach_fqdn)
        response = requests.get(
            url=url,
            headers=reach_token,
            params={"fetchLimit": "0", "searchString": network_name},
        )
    except requests.exceptions.RequestException:
        print("HTTP Request failed ()".format(inspect.currentframe().f_code.co_name))
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = "Bad response from Reach API call '{}' : HTTP {}".format(
            url, response.status_code
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    # see if prefixes match. If so, see if value (name) matches closely
    if response.status_code == requests.codes.ok:
        response_json = response.json()
        # print(json.dumps(response_json, indent=2))
        for this_network in response_json:
            this_network_name = this_network.get("name")

            if this_network_name is None:
                continue
            match_ratio = fuzz.ratio(this_network_name.lower(), network_name.lower())
            # print("{} - fuzz = {}".format(this_content_value, match_ratio))
            if match_ratio > 90:
                possible_matches.append(this_network)

    if len(possible_matches) == 1:
        possible_matches = possible_matches[0]
    else:
        logger_service.error(
            "Found {} matches for network {}".format(
                len(possible_matches), network_name
            )
        )
        raise Exception(
            "Found {} matches for network {}".format(
                len(possible_matches), network_name
            )
        )

    return possible_matches


def reach_get_territory_data_given_name(territory_name, reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :param territory_name: Name of the territory. Should usually be USA
    :return:
    """
    possible_matches = []
    try:
        url = "{}/reachengine/api/abc/territories".format(reach_fqdn)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        print("HTTP Request failed ()".format(inspect.currentframe().f_code.co_name))
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = "Bad response from Reach API call '{}' : HTTP {}".format(
            url, response.status_code
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    # see if prefixes match. If so, see if value (name) matches closely
    if response.status_code == requests.codes.ok:
        response_json = response.json()
        # print(json.dumps(response_json, indent=2))
        for this_territory in response_json:
            this_territory_name = this_territory.get("name")

            if this_territory_name is None:
                continue
            match_ratio = fuzz.ratio(
                this_territory_name.lower(), territory_name.lower()
            )
            # print("{} - fuzz = {}".format(this_content_value, match_ratio))
            if match_ratio > 90:
                possible_matches.append(this_territory)

    if len(possible_matches) == 1:
        possible_matches = possible_matches[0]
    else:
        raise Exception(
            "Found {} matches for territory {}".format(
                len(possible_matches), territory_name
            )
        )

    return possible_matches


def reach_get_genre_data_given_name(genre_name, reach_token, reach_fqdn):
    """
    :param genre_name:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """
    possible_matches = []
    try:
        url = "{}/reachengine/api/abc/genres".format(reach_fqdn)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        print("HTTP Request failed ()".format(inspect.currentframe().f_code.co_name))
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = "Bad response from Reach API call '{}' : HTTP {}".format(
            url, response.status_code
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    # see if a result matches.
    if response.status_code == requests.codes.ok:
        response_json = response.json()
        # print(json.dumps(response_json, indent=2))
        for this_genre in response_json:
            this_genre_name = this_genre.get("name")

            if this_genre_name is None:
                continue
            match_ratio = fuzz.ratio(this_genre_name.lower(), genre_name.lower())
            # print("{} - fuzz = {}".format(this_content_value, match_ratio))
            if match_ratio > 90:
                possible_matches.append(this_genre)

    if len(possible_matches) == 1:
        possible_matches = possible_matches[0]
    else:
        raise Exception(
            "Found {} matches for genre {}".format(len(possible_matches), genre_name)
        )

    return possible_matches


def reach_get_category_types(reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{}/reachengine/api/abc/packages/categoryTypes".format(reach_fqdn)
        response = requests.get(
            url=url, headers=reach_token, params={"view": "MINIMAL"}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_content_category_instance(category_id, reach_token, reach_fqdn):
    """
    :param category_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/categories/{1}".format(
            reach_fqdn, category_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_all_partners(reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/partners".format(reach_fqdn)
        response = requests.get(
            url=url,
            headers=reach_token,
            params={"fetchLimit": "-1", "includeChildren": False},
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_package_category_general_types(reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{}/reachengine/api/abc/packageCategories".format(reach_fqdn)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_package_categories_given_package_id(package_id, reach_token, reach_fqdn):
    """
    :param package_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/packages/{1}/categories".format(
            reach_fqdn, package_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_traffic_code_given_package_id(package_id, reach_token, reach_fqdn):
    """
    :param package_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/packages/{1}".format(reach_fqdn, package_id)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        traffic_code = (
            response_json.get("metadata", {})
            .get("properties", {})
            .get("trafficCode", {})
            .get("value", "")
        )
        return traffic_code


def reach_get_package_errors_given_package_id(package_id, reach_token, reach_fqdn):
    """This will get all current errors on a package.
        NOTE: This will _not_ return errors on missing content since at this point, we aren't checking for media
    :param package_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    all_errors = []

    try:
        url = "{0}/reachengine/api/abc/packages/{1}".format(reach_fqdn, package_id)
        params = {"view": "STATUS"}
        response = requests.get(url=url, params=params, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        validation_result = response_json.get("validationResult", [])
        for this_validation in validation_result:
            v_errors = this_validation.get("errors", [])
            for this_v_error in v_errors:
                this_v_error_description = this_v_error.get("description", "")
                if (
                    this_v_error_description != ""
                    and this_v_error_description != "Package does not have any contents"
                ):
                    all_errors.append(this_v_error.get("description", ""))

        return list(set(all_errors))

    return ["Unable to check package"]


def reach_get_categories_given_content_id(content_id, reach_token, reach_fqdn):
    """
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/{1}/categories".format(
            reach_fqdn, content_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_package_accept_templates(reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{}/reachengine/api/abc/packageAcceptTemplates".format(reach_fqdn)
        response = requests.get(
            url=url, headers=reach_token, params={"view": "EXTENDED"}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_media_accept_profiles(profile_type, reach_token, reach_fqdn):
    """
    :param profile_type: options are "OTHER". possibly "ARTWORK"
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{}/reachengine/api/abc/mediaAcceptProfiles".format(reach_fqdn)
        response = requests.get(
            url=url, headers=reach_token, params={"profileType": profile_type}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_distribution_grid_given_package_id(package_id, reach_token, reach_fqdn):
    """
    :param package_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/packages/{1}/partnerPackages".format(
            reach_fqdn, package_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_ratings(reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/ratings".format(reach_fqdn)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_rating_descriptors(reach_token, reach_fqdn):
    """
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/ratings/ratingDescriptors".format(reach_fqdn)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_seasons_given_series_id(content_id, reach_token, reach_fqdn):
    """
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/{1}/seasons".format(
            reach_fqdn, content_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_due_date_templates_given_partner_name(
    partner_name, reach_token, reach_fqdn
):
    """
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    # get all partners:
    all_partners = reach_get_all_partners(reach_token, reach_fqdn)
    matching_partner = {}
    matching_partner_id = ""
    for this_partner in all_partners:
        this_partner_name = this_partner.get("name")
        if this_partner_name == partner_name:
            matching_partner = this_partner
            matching_partner_id = this_partner.get("id")

    if matching_partner_id == "" and matching_partner == {}:
        logger_service.error('Partner "{}" not found'.format(partner_name))
        return []

    try:
        url = "{0}/reachengine/api/abc/partners/{1}/dueDateTemplates".format(
            reach_fqdn, matching_partner_id
        )
        response = requests.get(
            url=url, headers=reach_token, params={"includeHidden": False}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_seasons_given_package_id(package_id, reach_token, reach_fqdn):
    """
    :param package_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/packages/{1}/seasons".format(
            reach_fqdn, package_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_all_mvpd_seasons_given_series_id(
    content_id, mvpd_partner_id, reach_token, reach_fqdn
):
    """
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/{1}/seasons".format(
            reach_fqdn, content_id
        )
        response = requests.get(
            url=url, headers=reach_token, params={"mvpdPartnerId": mvpd_partner_id}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_mvpd_season_given_season_id(
    season_id, mvpd_partner_id, reach_token, reach_fqdn
):
    """
    :param mvpd_partner_id:
    :param season_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/seasons/{1}".format(
            reach_fqdn, season_id
        )
        response = requests.get(
            url=url, headers=reach_token, params={"mvpdPartnerId": mvpd_partner_id}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_network_extended(network_id, reach_token, reach_fqdn):
    """
    :param network_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/networks/{1}".format(reach_fqdn, network_id)
        response = requests.get(
            url=url, headers=reach_token, params={"view": "EXTENDED"}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_get_mvpd_data_for_partner_id_given_content_id(
    mvpd_partner_id, content_id, reach_token, reach_fqdn
):
    """
    :param mvpd_partner_id:
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.get(
            url=url, headers=reach_token, params={"mvpdPartnerId": mvpd_partner_id}
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


# def reach_get_mvpd_data_for_partner_id_given_content_id_and_(mvpd_partner_id, content_id, reach_token, reach_fqdn):
#     """
#     :param mvpd_partner_id:
#     :param content_id:
#     :param reach_fqdn:
#     :param reach_token:
#     :return:
#     """
#
#     this_func = inspect.currentframe().f_code.co_name
#
#     try:
#         url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
#         response = requests.get(
#             url=url,
#             headers=reach_token,
#             params={'mvpdPartnerId': mvpd_partner_id}
#         )
#     except requests.exceptions.RequestException:
#         err_msg = ("Function: {} - Error calling Reach API ({})".format(this_func, url))
#         logger_service.error(err_msg)
#         raise ConnectionError(err_msg)
#
#     if response.status_code != requests.codes.ok:
#         err_msg = "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(this_func, url,
#                                                                                           response.status_code)
#         logger_service.error(err_msg)
#         raise Exception(err_msg)
#
#     if response.status_code == requests.codes.ok:
#         response_json = response.json()
#         return response_json


def reach_get_language():
    """Just return the English language
    :return:
    """
    # TODO: maybe actually do something here? maybe?
    # GET https://staging8.reachengine.disney.com/reachengine/api/abc/languages
    # loop through to find English
    # for now - doing the lazy style and statically setting
    english_json = {
        "id": "200",
        "name": "English",
        "iso2Code": "EN",
        "boltLanguageId": "15",
        "wonderlandValue": "English US",
        "upLynkValue": "English",
        "isDefault": True,
        "aliasCount": "39",
    }

    return english_json


def reach_update_movie_mvpd_data_for_partner_id_given_content_id(
    mvpd_data_json, mvpd_partner_id, content_id, reach_token, reach_fqdn
):
    """
    :param mvpd_partner_id:
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.put(
            url=url,
            headers=reach_token,
            params={"mvpdPartnerId": mvpd_partner_id, "type": "Movie/Special"},
            json=mvpd_data_json,
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.no_content:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.no_content:
        # response_json = response.json()
        # return response_json
        return


def reach_update_series_mvpd_data_for_partner_id_given_content_id(
    mvpd_data_json, mvpd_partner_id, content_id, reach_token, reach_fqdn
):
    """
    :param mvpd_partner_id:
    :param content_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.put(
            url=url,
            headers=reach_token,
            params={"mvpdPartnerId": mvpd_partner_id, "type": "Movie/Special"},
            json=mvpd_data_json,
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.no_content:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.no_content:
        # response_json = response.json()
        # return response_json
        return


def reach_update_season_mvpd_data_for_partner_id_given_season_id(
    mvpd_data_json, mvpd_partner_id, season_id, reach_token, reach_fqdn
):
    """
    :param mvpd_partner_id:
    :param season_id:
    :param reach_fqdn:
    :param reach_token:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    try:
        url = "{0}/reachengine/api/abc/contents/seasons/{1}".format(
            reach_fqdn, season_id
        )
        response = requests.put(
            url=url,
            headers=reach_token,
            params={"mvpdPartnerId": mvpd_partner_id},
            json=mvpd_data_json,
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.no_content:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.no_content:
        # response_json = response.json()
        # return response_json
        return


def reach_update_package_given_pkg_json(pkg_json, reach_token, reach_fqdn):
    """Given a full package JSON file, update the package in Reach
    :param pkg_json:
    :param reach_token:
    :param reach_fqdn:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    pkg_id = pkg_json.get("id", "")

    if pkg_id == "":
        raise Exception("ID missing from package json")

    try:
        url = "{0}/reachengine/api/abc/packages/{1}".format(reach_fqdn, pkg_id)
        response = requests.post(url=url, headers=reach_token, json=pkg_json)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_update_package_external_id_and_system_name(
    package_id, new_ext_id, new_system_name, reach_token, reach_fqdn
):
    """
    Update the External ID and External System for a given package ID
    :param package_id:
    :param new_ext_id:
    :param new_system_name:
    :param reach_token:
    :param reach_fqdn:
    :return:
    """
    this_func_name = inspect.currentframe().f_code.co_name

    url = "{0}/reachengine/api/abc/packages/{1}".format(reach_fqdn, package_id)
    # url = ''.join([ipArg, '/reachengine/api/abc/packages/{}'.format(package_id)])
    # logger_service.info('{0} - trying to update {1}'.format(this_func_name, url))
    logger_service.info(
        "new ext id={}     new sys name={}".format(new_ext_id, new_system_name)
    )
    resp = requests.get(url=url, headers=reach_token)
    if resp.status_code == requests.codes.ok:
        rawJSON = resp.json()
    else:
        logger_service.error(
            "{0} - Problem getting ext ID & system name for package. Response code: {1}. Response: {2}".format(
                this_func_name, resp.status_code, resp.text
            )
        )
        # print('Problem getting ext ID & system name for package')
        # print('Response code: {0}'.format(resp.status_code))
        # #print('Does this user have sufficient privileges in Reach?')
        # print(resp.text)
        return False

    rawJSON["externalId"] = new_ext_id

    rawJSON["externalSystem"] = new_system_name

    resp_post = requests.post(url=url, json=rawJSON, headers=reach_token)
    if resp_post.status_code == requests.codes.ok:
        return True
    else:
        logger_service.error(
            "{0} - Problem posting ext ID & system name for package. Response code: {1}. Response: {2}".format(
                this_func_name, resp_post.status_code, resp_post.text
            )
        )
        # print('Problem posting ext ID & system name for package')
        # print('Response code: {0}'.format(resp_post.status_code))
        # #print('Does this user have sufficient privileges in Reach?')
        # print(resp_post.text)
        return False


def reach_update_distribution_grid_given_package_id(
    pkg_id, distribution_grid_subset, reach_token, reach_fqdn
):
    """Given a package ID, update the Distribution Grid in Reach
    :param distribution_grid_subset:
    :param pkg_id:
    :param reach_token:
    :param reach_fqdn:
    :return:
    """

    this_func = inspect.currentframe().f_code.co_name

    if pkg_id == "":
        raise Exception("ID missing from package json")

    try:
        url = "{0}/reachengine/api/abc/packages/{1}/partnerPackages".format(
            reach_fqdn, pkg_id
        )
        response = requests.put(
            url=url, headers=reach_token, json=distribution_grid_subset
        )
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


# def reach_add_content_given_name(series_name, series_prefix, network_name, territory_name, content_type, reach_token,
#                                  reach_fqdn):
#     """
#     Create new series or movie/special
#     :param reach_token:
#     :param series_prefix:
#     :param series_name:
#     :param network_name:
#     :param territory_name:
#     :param content_type: Must be Series or Movie/Special
#     :return:
#     """
#
#     # search and see if this series/special already exists
#     try:
#         existing_matches = reach_search_for_series_or_special(series_name, series_prefix, content_type, reach_token,
#                                                               reach_fqdn)
#     except:
#         return
#
#     # if there is an existing content match, throw exception
#     if len(existing_matches) > 0:
#         existing_content_id = existing_matches[0].get('id')
#         logger_service.warning("{0} '{1}' already exists (id {2}). Not creating a new one.".format(content_type, series_name,
#                                                                                            existing_content_id))
#         # raise Exception("{0} '{1}' already exists (id {2})".format(content_type, series_name, existing_content_id))
#         return existing_matches[0]
#
#     # get network_data based on network_name
#     # try:
#     network_data = reach_get_network_data_given_name(network_name, reach_token, reach_fqdn)
#     # except:
#     #     return
#
#     # get territory_data based on territory name (should be "USA")
#     # try:
#     territory_data = reach_get_territory_data_given_name(territory_name, reach_token, reach_fqdn)
#     # except:
#     #     return
#
#     # create json payload
#     new_sub_json = {"name": series_name,
#                     "network": network_data,
#                     "territory": territory_data,
#                     "type": content_type,
#                     "value": series_name,
#                     "prefix": series_prefix
#                     }
#     try:
#         url = "{0}/reachengine/api/abc/contents?type={1}".format(reach_fqdn, content_type)
#         response = requests.post(
#             url=url,
#             headers=reach_token,
#             json=new_sub_json
#         )
#     except requests.exceptions.RequestException:
#         err_msg = ("Error calling Reach API ({})".format(url))
#         logger_service.error(err_msg)
#         raise ConnectionError(err_msg)
#
#     # returns HTTP 201 if created
#     if response.status_code != requests.codes.created:
#         err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(url, response.status_code, response.reason)
#         logger_service.error(err_msg)
#         raise Exception(err_msg)
#
#     if response.status_code == requests.codes.created:
#         response_json = response.json()
#         return response_json


def reach_create_content(content_json, content_type, reach_token, reach_fqdn):
    """
    Create new series or movie/special
    :param content_json: Full JSON will all applicable data
    :param reach_token:
    :param content_type: Must be Series or Movie/Special
    :return:
    """

    # get network_data based on network_name
    # try:
    network_name = content_json["network"]["value"]
    try:
        network_data = reach_get_network_data_given_name(
            network_name, reach_token, reach_fqdn
        )
    except:
        logger_service.error(
            'Unable to match network in Reach for "{}"'.format(network_name)
        )
        network_data = {}

    if len(network_data) == 0 and type(network_data) == list:
        raise Exception('No network named "{}" in Reach'.format(network_name))

    # get territory_data based on territory name (should be "USA")
    territory_name = content_json["country"]["value"]
    try:
        territory_data = reach_get_territory_data_given_name(
            territory_name, reach_token, reach_fqdn
        )
    except:
        logger_service.error(
            'Unable to match territory in Reach for "{}"'.format(territory_name)
        )
        territory_data = {}

    # get genre_data based on genre
    genre_name = content_json["reach_genre"]["value"]
    if genre_name == "":
        genre_name = content_json["genre_code"]["value"]

    try:
        genre_data = reach_get_genre_data_given_name(
            genre_name, reach_token, reach_fqdn
        )
    except:
        logger_service.error(
            'Unable to match genre in Reach for "{}"'.format(genre_name)
        )
        genre_data = None

    # create json payload
    series_value = content_json["content_name"]["value"]
    series_name_human_readable = series_value
    if series_value[:2] == "A ":
        series_name_human_readable = series_value[2:] + ", A"
    if series_value[:4] == "The ":
        series_name_human_readable = series_value[4:] + ", The"

    new_content_json = {
        "name": series_name_human_readable,
        "network": network_data,
        "territory": territory_data,
        "type": content_type,
        "value": series_value,
        "prefix": content_json.get("content_prefix", {}).get("value"),
        "shortDescription": content_json.get("content_short_synopsis", {}).get("value"),
        "longDescription": content_json.get("content_long_synopsis", {}).get("value"),
        "startYear": content_json.get("content_start_year", {}).get("value"),
        "genre": genre_data,
        "copyright": content_json.get("copyright", {}).get("value"),
        "autoKeywords": content_json.get("keywords", {}).get("value"),
        "radarGroupId": content_json.get("radar_group_id", {}).get("value"),
        "metadata": {
            "properties": {
                "comscoreC6": {
                    "value": content_json.get("comscore_c6", {}).get("value"),
                    "valueOverridden": False,
                    "required": False,
                },
                "dATGGoPubFolder": {
                    "value": content_json.get("cms_folder_name", {}).get("value"),
                    "valueOverridden": False,
                    "required": False,
                },
            }
        },
    }
    content_tms = content_json.get("tms_id", {}).get("value", "")
    if content_tms != "":
        new_content_json["metadata"]["properties"]["tMSSeriesID"] = {
            "value": content_tms
        }

    # check series json - remove any items that are blank
    # for this_key in new_content_json.keys():  # this breaks in Python 3
    for this_key in list(new_content_json):
        if new_content_json.get(this_key) == "":
            del new_content_json[this_key]

    try:
        url = "{0}/reachengine/api/abc/contents?type={1}".format(
            reach_fqdn, content_type
        )
        response = requests.post(url=url, headers=reach_token, json=new_content_json)
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    # returns HTTP 201 if created
    if response.status_code != requests.codes.created:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.created:
        response_json = response.json()
        return response_json


def reach_create_season_given_content_id(
    content_json, content_id, reach_token, reach_fqdn
):
    """
    Create new series or movie/special
    :param content_json: Full JSON will all applicable data
    :param reach_token:
    :param content_id: the id of the series you want to add a season to
    :return:
    """

    # create json payload
    season_value = content_json["season_num"]["value"]
    new_season_json = {
        "name": "Season " + season_value,
        "value": season_value,
        "shortDescription": content_json["season_short_synopsis"]["value"],
        "longDescription": content_json["season_long_synopsis"]["value"],
        "metadata": {"properties": {}},
    }

    try:
        url = "{0}/reachengine/api/abc/contents/{1}/seasons".format(
            reach_fqdn, content_id
        )
        response = requests.post(url=url, headers=reach_token, json=new_season_json)
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    # returns HTTP 201 if created
    if response.status_code != requests.codes.created:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.created:
        response_json = response.json()
        return response_json


def reach_create_package(content_json, reach_token, reach_fqdn):
    """
    Create new package
    :param content_json: Full JSON will all applicable data. We will pull relevant data from this.
    :param reach_token:
    :return:
    """
    program_type = content_json.get("program_type", {}).get("value", "")
    content_type = content_json.get("content_type", {}).get("value", "")
    content_name = content_json.get("content_name", {}).get("value", "")
    network_name = content_json.get("network", {}).get("value", "")
    # season_num = content_json.get('season_num')
    episode_title = content_json.get("episode_title", {}).get("value", "")

    if program_type == "movie" or program_type == "special":
        program_type = "Movie/Special"
    elif program_type == "series":
        program_type = "Series"
    elif program_type == "acquired":
        program_type = "Series"

    if "long" in content_type.lower():
        categoryType = {"id": "201", "name": "Long Form"}
    elif "short" in content_type.lower():
        categoryType = {"id": "200", "name": "Short Form"}
    else:
        raise Exception('Unknown/unsupported category type "{}"'.format(program_type))

    found_content = reach_search_for_series_or_special_or_movie(
        content_name, program_type, network_name, reach_token, reach_fqdn
    )
    if len(found_content) != 1:
        logger_service.error(
            'Found {} matches for content name "{}" of type {}'.format(
                len(found_content), content_name, program_type
            )
        )
        raise Exception(
            'Found {} matches for content name "{}"'.format(
                len(found_content), content_name
            )
        )
    found_content_json = found_content[0]
    content_id = found_content_json.get("id")

    # need to get the Content MINIMAL payload
    content_minimal = reach_get_content_minimal(content_id, reach_token, reach_fqdn)

    new_pkg_payload = {
        "autoDeliver": False,
        "categoryType": categoryType,
        "content": content_minimal,
        "name": episode_title,
        "purgeOnIngest": False,
    }

    # call API to make the package
    try:
        url = "{0}/reachengine/api/abc/packages".format(reach_fqdn, content_id)
        response = _make_reach_api_post(url=url, headers=reach_token, json=new_pkg_payload)
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    # returns HTTP 201 if created
    if response.status_code != requests.codes.created:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    new_pkg_json = response.json()
    return new_pkg_json


def reach_create_collaborator(
    actor_first, actor_last, imdb_id, reach_token, reach_fqdn
):
    """
    Create new collaborator in Reach
    :param actor_last: actor last name
    :param actor_first: actor first name
    :param reach_token:
    :return:
    """

    # create json payload
    new_actor_json = {
        "displayName": actor_first + " " + actor_last,
        "sortName": actor_last + ", " + actor_first,
        "iMDbNumber": imdb_id,
    }

    try:
        url = "{0}/reachengine/api/abc/collaborators".format(reach_fqdn)
        response = requests.post(url=url, headers=reach_token, json=new_actor_json)
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    # returns HTTP 201 if created
    if response.status_code != requests.codes.created:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.created:
        response_json = response.json()
        return response_json


def reach_create_content_artwork_container_given_content_id(
    content_id, reach_token, reach_fqdn
):
    """
    Create new artwork container for series
    :param content_id:
    :param reach_token:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name

    # get content name
    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        content_json = response.json()

    if (
        content_json.get("prefix") is not None
        and "movie" not in content_json.get("type", "movie").lower()
    ):
        artwork_container_name = content_json.get("prefix") + " Key Art"
    else:
        artwork_container_name = content_json.get("name") + " Key Art"

    content_type = content_json.get("type")
    if content_type == "Movie/Special":
        content_type_for_url = "movies"
    elif content_type == "Movie/Special":
        pass

    artwork_json = {"name": artwork_container_name}

    # add the artwork name to Series/Movie level
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/artwork".format(
            reach_fqdn, content_id
        )
        response = requests.post(url=url, headers=reach_token, json=artwork_json)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        new_art_id = response_json.get("id", "")
        if new_art_id != "":
            #     new_art_url = '{0}/reachengine/api/abc/artwork/{1}'.format(reach_fqdn, new_art_id)
            #     new_art_url_webUI = '{0}/meta/series/edit/{1}/artwork/edit/{2}'.format(reach_fqdn, content_id, new_art_id)
            return new_art_id
        else:
            return ""


def reach_get_content_artwork_container_given_content_id(
    content_id, reach_token, reach_fqdn
):
    """
    Get  artwork container for series if it exists (note: there should be only one artwork container - it is at series/movie level.

    :param content_id:
    :param reach_token:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name

    # get content name
    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        content_json = response.json()

    if content_json.get("type") == "Movie/Special":
        artwork_container_name = content_json.get("name") + " Key Art"
    else:
        if content_json.get("prefix") is not None:
            artwork_container_name = content_json.get("prefix") + " Key Art"
        else:
            artwork_container_name = content_json.get("name") + " Key Art"

    # content_type = content_json.get('type')
    # if content_type == 'Movie/Special':
    #     content_type_for_url = 'movies'
    # elif content_type == 'Movie/Special':
    #     pass

    # artwork_json = {'name': artwork_container_name}

    # add the artwork name  (TODO: this creates the actual container - only needs to be made ONCE. check if it exists before making it again)
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/artwork".format(
            reach_fqdn, content_id
        )
        params = {"fetchLimit": 0, "includeHidden": False}
        response = requests.get(url=url, headers=reach_token, params=params)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        for this_art in response_json:
            art_name = this_art.get("name")
            art_id = this_art.get("id")
            if art_name.endswith("Key Art"):
                # if art_name == artwork_container_name:
                return art_id

        return ""
    else:
        return ""


def reach_get_artwork_instances_given_content_id_and_season_id(
    content_id, season_id, reach_token, reach_fqdn
):
    """
    Get the actual artwork files - see if they have already been populated in Reach.

    :param content_id:
    :param reach_token:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name

    # get content name
    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        content_json = response.json()

    # get all artwork
    try:
        params = {"fetchLimit": 0, "includeHidden": False}
        url = "{0}/reachengine/api/abc/contents/{1}/artwork".format(
            reach_fqdn, content_id
        )
        response = requests.get(url=url, headers=reach_token, params=params)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        all_art_containers = response.json()

    if content_json.get("prefix") is not None:
        artwork_container_name = content_json.get("prefix") + " Key Art"
    else:
        artwork_container_name = content_json.get("name") + " Key Art"

    art_asset_ids = {"large": "", "small": ""}
    for this_art_container in all_art_containers:
        all_art_assets_in_container = this_art_container.get("assets", [])
        for this_art_asset in all_art_assets_in_container:
            art_type = this_art_asset.get("type")
            art_id = this_art_asset.get("id")
            art_season_id = this_art_asset.get("seasonId", "")
            art_profile_name = this_art_asset.get("acceptProfile", {}).get("name", "")
            if (
                art_type == "SeasonArtwork"
            ):  # looking for SeasonArtwork because this function is specifically looking for season art
                if art_season_id == season_id:
                    if art_profile_name == "JPG 288x432 English (DirecTV & Verizon)":
                        art_asset_ids["small"] = art_id
                    if art_profile_name == "JPG 2:3 Ratio - English (from Gracenote)":
                        art_asset_ids["large"] = art_id

                if art_asset_ids["small"] != "" and art_asset_ids["large"] != "":
                    break

        if art_asset_ids["small"] != "" and art_asset_ids["large"] != "":
            break

    return art_asset_ids


def reach_get_artwork_instances_given_content_id_movies(
    content_id, reach_token, reach_fqdn
):
    """
    Get the actual artwork files - see if they have already been populated in Reach.

    :param content_id:
    :param reach_token:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name

    # get content name
    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        content_json = response.json()

    # get all artwork
    try:
        params = {"fetchLimit": 0, "includeHidden": False}
        url = "{0}/reachengine/api/abc/contents/{1}/artwork".format(
            reach_fqdn, content_id
        )
        response = requests.get(url=url, headers=reach_token, params=params)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        all_art_containers = response.json()

    if content_json.get("prefix") is not None:
        artwork_container_name = content_json.get("prefix") + " Key Art"
    else:
        artwork_container_name = content_json.get("name") + " Key Art"

    art_asset_ids = {"large": "", "small": ""}
    for this_art_container in all_art_containers:
        all_art_assets_in_container = this_art_container.get("assets", [])
        for this_art_asset in all_art_assets_in_container:
            art_type = this_art_asset.get("type")
            art_id = this_art_asset.get("id")
            art_profile_name = this_art_asset.get("acceptProfile", {}).get("name", "")
            if (
                art_type == "Artwork"
            ):  # looking for Artwork because this function is specifically looking for movie art
                if art_profile_name == "JPG 288x432 English (DirecTV & Verizon)":
                    art_asset_ids["small"] = art_id
                if art_profile_name == "JPG 2:3 Ratio - English (from Gracenote)":
                    art_asset_ids["large"] = art_id

                if art_asset_ids["small"] != "" and art_asset_ids["large"] != "":
                    break

        if art_asset_ids["small"] != "" and art_asset_ids["large"] != "":
            break

    return art_asset_ids


def reach_create_artwork_container_given_content_id(
    content_id, reach_token, reach_fqdn
):
    """
    Create new artwork container for series (note: there is only one artwork container - it is at series/movie level.
    But the returned web UI should go to the season level
    :param content_id:
    :param reach_token:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name

    # get content name
    try:
        url = "{0}/reachengine/api/abc/contents/{1}".format(reach_fqdn, content_id)
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        content_json = response.json()

    if content_json.get("type", "") == "Movie/Special":
        is_movie = True
    else:
        is_movie = False

    if is_movie:
        artwork_container_name = content_json.get("name") + " Key Art"
    else:
        if content_json.get("prefix") is not None:
            artwork_container_name = content_json.get("prefix") + " Key Art"
        else:
            artwork_container_name = content_json.get("name") + " Key Art"

    # content_type = content_json.get('type')
    # if content_type == 'Movie/Special':
    #     content_type_for_url = 'movies'
    # elif content_type == 'Movie/Special':
    #     pass

    artwork_json = {"name": artwork_container_name}

    # add the artwork name  (TODO: this creates the actual container - only needs to be made ONCE. check if it exists before making it again)
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/artwork".format(
            reach_fqdn, content_id
        )
        response = requests.post(url=url, headers=reach_token, json=artwork_json)
    except requests.exceptions.RequestException:
        err_msg = "Function: {} - Error calling Reach API ({})".format(this_func, url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = (
            "Function: {} - Bad response from Reach API call '{}' : HTTP {}".format(
                this_func, url, response.status_code
            )
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        new_art_id = response_json.get("id", "")
        return new_art_id
    else:
        return ""


def reach_add_collaborator_given_content_id(
    actor, character_name, content_id, reach_token, reach_fqdn
):
    """
    Create new collaborator in Reach
    :param character_name: the character in the show
    :param actor: actor JSON
    :param content_id: content id in Reach
    :param reach_token:
    :return:
    """

    # create json payload
    actor_json = {
        "character": character_name,
        "collaborator": actor,
        "contentId": content_id,
        "type": "Actor",
    }
    params = {"type": "Actor"}

    try:
        url = "{0}/reachengine/api/abc/contents/{1}/actors".format(
            reach_fqdn, content_id
        )
        response = requests.post(
            url=url, headers=reach_token, json=actor_json, params=params
        )
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        response_json = response.json()
        return response_json


def reach_add_collaborator_given_content_id_to_season_id(
    actor_name, character_name, content_id, season_id, reach_token, reach_fqdn
):
    """
    Create new collaborator in Reach
    :param season_id: should be the id of the season, not the season number
    :param character_name: the character in the show
    :param actor_name: actor name string like "First Last"
    :param content_id: content id in Reach
    :param reach_token:
    :return:
    """

    # get current season data:
    try:
        url = "{0}/reachengine/api/abc/contents/seasons/{1}".format(
            reach_fqdn, season_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        season_json = response.json()
    else:
        season_json = {}

    # get actor data from series:
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/actors".format(
            reach_fqdn, content_id
        )
        response = requests.get(url=url, headers=reach_token)
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.ok:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.ok:
        actor_json_list = response.json()
    else:
        actor_json_list = []

    # add new actor to season
    single_actor_json = {}
    for this_actor in actor_json_list:
        this_actor_display_name = this_actor.get("collaborator", {}).get("displayName")
        this_actor_char_name = this_actor.get("character", "")
        if (this_actor_display_name.strip() == actor_name.strip()) and (
            this_actor_char_name.strip() == character_name.strip()
        ):
            single_actor_json = this_actor
            break

    if len(single_actor_json) == 0:
        logger_service.error('Actor "{}" is not in Series actors'.format())
        return

    curr_season_actors = season_json.get("actors")
    actor_already_added = False
    for this_curr_actor in curr_season_actors:
        if this_curr_actor.get("collaborator", {}).get("displayName") == actor_name:
            logger_service.info(
                'Collaborator "{}" already added to season'.format(actor_name)
            )
            actor_already_added = True

    # update actor list
    if actor_already_added is False:
        season_json["actors"].append(single_actor_json)
        logger_service.info(
            "Adding actor {} ({}) to season id {} of content id {}".format(
                actor_name, character_name, season_id, content_id
            )
        )
        try:
            url = url = "{0}/reachengine/api/abc/contents/seasons/{1}".format(
                reach_fqdn, season_id
            )
            response = requests.put(url=url, headers=reach_token, json=season_json)
        except requests.exceptions.RequestException:
            err_msg = "Error calling Reach API ({})".format(url)
            logger_service.error(err_msg)
            raise ConnectionError(err_msg)

        if (
            response.status_code != requests.codes.no_content
            and response.status_code != requests.codes.ok
        ):
            err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
                url, response.status_code, response.reason
            )
            logger_service.error(err_msg)
            raise Exception(err_msg)


def reach_add_media_profiles_given_content_id(
    content_id, profiles_to_add_list, reach_token, reach_fqdn
):
    """Used to add media profiles to a series or movie. Media profiles are like "MASTER ProRes" or MXF or whatever.
    :param content_id:
    :param profiles_to_add_list: a list of key/value pairs, each item a json of "type
    :param reach_token:
    :param reach_fqdn:
    :return:

    Note: it's all about getting those IDs. There's a lot here that seems superfluous - this is because we need to grab
    various IDs for use later on.

    example of profiles_to_add_list variable:
    [ {'type': 'Long Form Content',
       'profile': 'PNG 1920x1080',
       'validation': 'REQUIRED'} ]
    """
    # get all category types - should be: [{"id":"201","name":"Long Form"},{"id":"200","name":"Short Form"}]
    all_media_categories = reach_get_category_types(reach_token, reach_fqdn)

    short_form_category = None
    long_form_category = None
    for this_cat in all_media_categories:
        if this_cat.get("name") == "Long Form":
            long_form_category = this_cat
        if this_cat.get("name") == "Short Form":
            short_form_category = this_cat

    # get package accept templates - response list will contain profiles for these package types:
    #       "Ads & Prerolls", "Long Form Content", "Short Form Content"
    all_package_accept_templates = reach_get_package_accept_templates(
        reach_token, reach_fqdn
    )
    for (
        this_pkg_template
    ) in all_package_accept_templates:  # loop through "Ads & Prerolls", Long, Short
        if this_pkg_template.get("name") == "Long Form Content":
            long_form_templates = this_pkg_template
            long_form_header = copy.deepcopy(this_pkg_template)
            try:
                del long_form_header["mediaAcceptProfiles"]
            except:
                pass

        if this_pkg_template.get("name") == "Short Form Content":
            short_form_templates = this_pkg_template
            short_form_header = copy.deepcopy(this_pkg_template)
            try:
                del short_form_header["mediaAcceptProfiles"]
            except:
                pass

    # create a placeholder data structure with all "NOT APPLICABLE" values
    # later we will use PUT to update values

    placeholder_data = []
    # first, determine if we need short, long or both
    # go through "profiles_to_add_list" variable list
    has_short_form_profiles = False
    has_long_form_profiles = False
    for this_profile in profiles_to_add_list:
        this_profile_type = this_profile.get("type")
        if this_profile_type == "Long Form Content":
            has_long_form_profiles = True
        if this_profile_type == "Short Form Content":
            has_short_form_profiles = True

    if has_short_form_profiles:
        short_members = []
        for this_short_form_profile in short_form_templates["mediaAcceptProfiles"]:
            temp_profile = {}
            temp_profile["acceptProfile"] = this_short_form_profile
            temp_profile["validationType"] = "NOT_APPLICABLE"
            short_members.append(temp_profile)

        short_form_placeholder = {
            "categoryType": short_form_category,
            "members": short_members,
            "packageAcceptTemplate": short_form_header,
        }
        placeholder_data.append(short_form_placeholder)

    if has_long_form_profiles:
        long_members = []
        for this_long_form_profile in long_form_templates["mediaAcceptProfiles"]:
            temp_profile = {}
            temp_profile["acceptProfile"] = this_long_form_profile
            temp_profile["validationType"] = "NOT_APPLICABLE"
            long_members.append(temp_profile)

        long_form_placeholder = {
            "categoryType": long_form_category,
            "members": long_members,
            "packageAcceptTemplate": long_form_header,
        }
        placeholder_data.append(long_form_placeholder)

    # go through profiles_to_add_list list (the profiles we want to add),
    # see where they are in all_package_accept_templates.
    #    - set the validation to whatever is in the profiles variable
    for this_profile_to_add in profiles_to_add_list:
        this_profile_type = this_profile_to_add.get("type")
        this_profile_profile = this_profile_to_add.get("profile")
        this_profile_validation = this_profile_to_add.get("validation")
        if (
            this_profile_type is None
            or this_profile_profile is None
            or this_profile_validation is None
        ):
            logger_service.warning(
                "Bad profile data sent. Missing data in: {}".format(
                    json.dumps(this_profile)
                )
            )
            continue

        # we want to only get specific
        package_accept_templates_subset = None
        for this_placeholder in placeholder_data:
            short_check = (
                this_placeholder.get("categoryType").get("name") == "Short Form"
            ) and this_profile_type == "Short Form Content"
            long_check = (
                this_placeholder.get("categoryType").get("name") == "Long Form"
            ) and this_profile_type == "Long Form Content"

            if short_check or long_check:
                for this_member in this_placeholder.get("members"):
                    if (
                        this_member.get("acceptProfile").get("name")
                        == this_profile_profile
                    ):
                        this_member["validationType"] = this_profile_validation

    # Then add the profiles (id is 10608 - this is id of the content)
    # PUT https://staging8.reachengine.disney.com/reachengine/api/abc/contents/10608/mediaProfiles
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/mediaProfiles".format(
            reach_fqdn, content_id
        )
        response = requests.put(url=url, headers=reach_token, json=placeholder_data)
    except requests.exceptions.RequestException:
        # print('HTTP PUT Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Error calling Reach API PUT ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)


def reach_update_media_profiles_given_content_id(
    content_id, profiles_to_add_list, reach_token, reach_fqdn
):
    """Series or movie already exists. Let's check its media profiles and add any new ones from profiles_to_add_list
    :param content_id:
    :param profiles_to_add_list: a list of key/value pairs, each item a json of "type
    :param reach_token:
    :param reach_fqdn:
    :return:

    Note: it's all about getting those IDs. There's a lot here that seems superfluous - this is because we need to grab
    various IDs for use later on.

    example of profiles_to_add_list variable:
    [ {'type': 'Long Form Content',
       'profile': 'PNG 1920x1080',
       'validation': 'REQUIRED'} ]
    """
    # get existing media profiles
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/mediaProfiles".format(
            reach_fqdn, content_id
        )
        response = requests.get(url=url, headers=reach_token)
        existing_profiles = response.json()
    except requests.exceptions.RequestException:
        err_msg = "Error calling Reach API GET ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    has_existing_long = False
    has_existing_short = False
    for this_existing in existing_profiles:
        this_existing_type_name = this_existing.get("categoryType", {}).get("name")
        if this_existing_type_name == "Long Form":
            has_existing_long = True
            existing_long_profiles = this_existing
        if this_existing_type_name == "Short Form":
            has_existing_short = True
            existing_short_profiles = this_existing

    # get all category types - should be: [{"id":"201","name":"Long Form"},{"id":"200","name":"Short Form"}]
    all_media_categories = reach_get_category_types(reach_token, reach_fqdn)
    short_form_category = None
    long_form_category = None
    for this_cat in all_media_categories:
        if this_cat.get("name") == "Long Form":
            long_form_category = this_cat
        if this_cat.get("name") == "Short Form":
            short_form_category = this_cat

    # get package accept templates - response list will contain profiles for these package types:
    #       "Ads & Prerolls", "Long Form Content", "Short Form Content"
    all_package_accept_templates = reach_get_package_accept_templates(
        reach_token, reach_fqdn
    )
    for (
        this_pkg_template
    ) in all_package_accept_templates:  # loop through "Ads & Prerolls", Long, Short
        if this_pkg_template.get("name") == "Long Form Content":
            long_form_templates = this_pkg_template
            long_form_header = copy.deepcopy(this_pkg_template)
            try:
                del long_form_header["mediaAcceptProfiles"]
            except:
                pass

        if this_pkg_template.get("name") == "Short Form Content":
            short_form_templates = this_pkg_template
            short_form_header = copy.deepcopy(this_pkg_template)
            try:
                del short_form_header["mediaAcceptProfiles"]
            except:
                pass

    # create a placeholder data structure with all "NOT APPLICABLE" values BUT ONLY:
    #   -IF NEEDED AND
    #   -NOT ALREADY EXISTING
    # later we will use PUT to update values

    # placeholder_data = []
    # first, determine if we need short, long or both
    # go through "profiles_to_add_list" variable list
    has_short_form_profiles = False
    has_long_form_profiles = False
    for this_profile in profiles_to_add_list:
        this_profile_type = this_profile.get("type")
        if this_profile_type == "Long Form Content":
            has_long_form_profiles = True
        if this_profile_type == "Short Form Content":
            has_short_form_profiles = True

    if has_short_form_profiles:
        if not has_existing_short:
            short_members = []
            for this_short_form_profile in short_form_templates["mediaAcceptProfiles"]:
                temp_profile = {}
                temp_profile["acceptProfile"] = this_short_form_profile
                temp_profile["validationType"] = "NOT_APPLICABLE"
                short_members.append(temp_profile)

            short_form_placeholder = {
                "categoryType": short_form_category,
                "members": short_members,
                "packageAcceptTemplate": short_form_header,
            }
            existing_profiles.append(short_form_placeholder)
            # placeholder_data.append(short_form_placeholder)

    if has_long_form_profiles:
        if not has_existing_long:
            long_members = []
            for this_long_form_profile in long_form_templates["mediaAcceptProfiles"]:
                temp_profile = {}
                temp_profile["acceptProfile"] = this_long_form_profile
                temp_profile["validationType"] = "NOT_APPLICABLE"
                long_members.append(temp_profile)

            long_form_placeholder = {
                "categoryType": long_form_category,
                "members": long_members,
                "packageAcceptTemplate": long_form_header,
            }
            existing_profiles.append(long_form_placeholder)
            # placeholder_data.append(long_form_placeholder)

    # go through profiles_to_add_list list (the profiles we want to add),
    # see where they are in all_package_accept_templates.
    #    - set the validation to whatever is in the profiles variable
    for this_profile_to_add in profiles_to_add_list:
        this_profile_type = this_profile_to_add.get("type")
        this_profile_profile = this_profile_to_add.get("profile")
        this_profile_validation = this_profile_to_add.get("validation")
        if (
            this_profile_type is None
            or this_profile_profile is None
            or this_profile_validation is None
        ):
            logger_service.warning(
                "Bad profile data sent. Missing data in: {}".format(
                    json.dumps(this_profile)
                )
            )
            continue

        # we want to only get specific
        package_accept_templates_subset = None
        for this_placeholder in existing_profiles:
            short_check = (
                this_placeholder.get("categoryType").get("name") == "Short Form"
            ) and this_profile_type == "Short Form Content"
            long_check = (
                this_placeholder.get("categoryType").get("name") == "Long Form"
            ) and this_profile_type == "Long Form Content"

            if short_check or long_check:
                for this_member in this_placeholder.get("members"):
                    if (
                        this_member.get("acceptProfile").get("name")
                        == this_profile_profile
                    ):
                        this_member["validationType"] = this_profile_validation

    # Then add the profiles (id is 10608 - this is id of the content)
    # PUT https://staging8.reachengine.disney.com/reachengine/api/abc/contents/10608/mediaProfiles
    try:
        url = "{0}/reachengine/api/abc/contents/{1}/mediaProfiles".format(
            reach_fqdn, content_id
        )
        response = requests.put(url=url, headers=reach_token, json=existing_profiles)
    except requests.exceptions.RequestException:
        # print('HTTP PUT Request failed ()'.format(inspect.currentframe().f_code.co_name))
        err_msg = "Error calling Reach API PUT ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)


def reach_add_category_given_content_id(
    content_id, category_to_add, reach_token, reach_fqdn
):
    """
    :param content_id:
    :param category_to_add: something like "Episode", "Episode C Type" or "Movie (Theatrical Formatted for TV)"
    :param reach_token:
    :param reach_fqdn:
    :return:
    """
    # get all category types first:
    # GET https://staging8.reachengine.disney.com/reachengine/api/abc/packages/categoryTypes?view=MINIMAL
    # try:
    all_pkg_categories = reach_get_package_category_general_types(
        reach_token, reach_fqdn
    )
    # all_pkg_categories should be: [{"id":"201","name":"Long Form"},{"id":"200","name":"Short Form"}]

    pkg_category_to_add = {}
    for this_cat in all_pkg_categories:
        # logger_service.info('  cat check: {}'.format(this_cat.get('name')))
        if this_cat.get("name") == category_to_add:
            pkg_category_to_add = this_cat

    if len(pkg_category_to_add) == 0:  # still empty
        return

    category_json_with_cc = {
        "category": pkg_category_to_add,
        "daysOfWeekSchedule": [],
        "id": None,
        "videoRoles": [
            {
                "closedCaptioningAcceptTemplate": {
                    "displayMode": "ANY",
                    "languageRoleMap": [
                        {
                            "language": {
                                "aliasCount": "39",
                                "boltLanguageId": "15",
                                "id": "200",
                                "isDefault": True,
                                "iso2Code": "EN",
                                "name": "English",
                                "upLynkValue": "English",
                                "wonderlandValue": "English US",
                            },
                            "role": {
                                "aliasCount": "0",
                                "dvsFlag": False,
                                "id": "201",
                                "isDefault": True,
                                "name": "Spoken Language",
                                "type": "CC",
                            },
                        }
                    ],
                    "type": "CC",
                },
                "role": {
                    "aliasCount": "0",
                    "dvsFlag": False,
                    "id": "260",
                    "isDefault": True,
                    "name": "Main Video",
                    "type": "Video",
                },
            }
        ],
    }

    category_json_no_cc = {
        "category": pkg_category_to_add,
        "daysOfWeekSchedule": [],
        "id": None,
        "videoRoles": [
            {
                "role": {
                    "aliasCount": "0",
                    "dvsFlag": False,
                    "id": "260",
                    "isDefault": True,
                    "name": "Main Video",
                    "type": "Video",
                }
            }
        ],
    }

    if "d type" in category_to_add.lower():
        category_json = category_json_no_cc
    else:
        category_json = category_json_with_cc

    try:
        url = "{0}/reachengine/api/abc/contents/{1}/categories".format(
            reach_fqdn, content_id
        )
        response = requests.post(url=url, headers=reach_token, json=category_json)
        response_json = response.json()
        response_code = response.status_code
        new_cat_id = response_json.get("id", "")
        logger_service.info(
            "  Category creation response code: {}.  ID={}".format(
                response_code, new_cat_id
            )
        )
    except requests.exceptions.RequestException:
        print(
            "HTTP POST Request failed ()".format(inspect.currentframe().f_code.co_name)
        )
        err_msg = "Error calling Reach API POST ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)


def reach_add_season_given_content_id(content_id, season_num, reach_token, reach_fqdn):
    """
    :param content_id:
    :param season_num: "Episode", "Episode C Type" or "Movie (Theatrical Formatted for TV)"
    :param reach_token:
    :param reach_fqdn:
    :return:
    """

    season_json = {
        "metadata": {"properties": {}},
        "name": "Season " + str(season_num),
        "value": str(season_num),
    }

    try:
        url = "{0}/reachengine/api/abc/contents/{1}/seasons".format(
            reach_fqdn, content_id
        )
        response = requests.post(url=url, headers=reach_token, json=season_json)
    except requests.exceptions.RequestException:
        print(
            "HTTP POST Request failed ()".format(inspect.currentframe().f_code.co_name)
        )
        err_msg = "Error calling Reach API POST ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.created:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        raise Exception(err_msg)

    if response.status_code == requests.codes.created:
        response_json = response.json()
        return response_json


def reach_add_artwork_file_to_artwork_container(
    reach_content_id,
    season_id,
    artwork_id,
    artwork_path,
    art_size_and_type,
    reach_token,
    reach_fqdn,
):
    """
    Attach artwork image path to an artwork container. The artwork_path must already be in a path that Reach can access.
    This will also convert the path from a "real" path to one usable by the Reach Virtual File System (VFS)
    Example of conversion: something like C:\\path\\to\\virtualfilesystem\\file to /virtualfilesystem/file
    This is necessary for the Reach API to work

    :param reach_content_id:
    :param season_id:
    :param artwork_id:
    :param small_artwork_path:
    :param art_size_and_type:
    :param reach_token:
    :param reach_address:
    :return:
    """
    # 4 = small_poster or large_poster
    if art_size_and_type == "small_poster":
        matching_map = "JPG 288x432 English (DirecTV & Verizon)"
    elif art_size_and_type == "large_poster":
        matching_map = "JPG 2:3 Ratio - English (from Gracenote)"
    else:
        logger_service.error(
            "unsupported art_size_and_type. Expecting small_poster or large_poster. Value provided: {}".format(
                art_size_and_type
            )
        )
        return

    # get all media profiles
    all_maps = reach_get_media_accept_profiles("OTHER", reach_token, reach_fqdn)
    map_json = {}
    for this_map in all_maps:
        this_map_name = this_map.get("name", "")
        if this_map_name == matching_map:
            map_json = this_map
            break

    lang = reach_get_language()  # static English

    if (
        artwork_path.lower().startswith("b:")
        or artwork_path.lower().startswith("t:")
        or artwork_path.lower().startswith("z:")
    ):
        virtual_path = convert_windows_real_filepath_to_virtual_filepath(artwork_path)
    else:
        virtual_path = convert_linux_real_filepath_to_virtual_filepath(artwork_path)

    if virtual_path == "":
        logger_service.error(
            "Adding artwork: missing virtual path conversion - stopping"
        )
        return
    if map_json == {}:
        logger_service.error(
            "Adding artwork: missing Media Accept Profile (MAP) - stopping"
        )
        return

    if season_id != "":
        # for season level - like series
        artwork_body_json = {
            "acceptProfile": map_json,
            "language": lang,
            "pristine": False,
            "seasonId": season_id,
            "type": "SeasonArtwork",
            "virtualPath": virtual_path,
        }
    else:
        # for main level - like movies
        artwork_body_json = {
            "acceptProfile": map_json,
            "language": lang,
            "pristine": False,
            "type": "Artwork",
            "virtualPath": virtual_path,
        }

    try:
        url = "{0}/reachengine/api/abc/artwork/{1}/assets".format(
            reach_fqdn, artwork_id
        )
        response = requests.post(url=url, headers=reach_token, json=artwork_body_json)
    except requests.exceptions.RequestException:
        print(
            "HTTP POST Request failed ()".format(inspect.currentframe().f_code.co_name)
        )
        err_msg = "Error calling Reach API POST ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)

    if response.status_code != requests.codes.created:
        err_msg = "Bad response from Reach API call '{}' : HTTP {} {}".format(
            url, response.status_code, response.reason
        )
        logger_service.error(err_msg)
        # raise Exception(err_msg)
        return

    if response.status_code == requests.codes.created:
        response_json = response.json()
        return response_json


def reach_check_if_category_name_already_subscribed_to_partner_name_given_content_id(
    category_name, partner_name, content_id, reach_token, reach_fqdn
):
    """

    :param category_name:
    :param partner_name:
    :param content_id:
    :param reach_token:
    :param reach_fqdn:
    :return:
    """
    # get content first:
    logger_service.info("checking if {} already subscribed".format(partner_name))
    content = reach_get_content(content_id, reach_token, reach_fqdn)
    content_name = content.get("name", "")
    content_prefix = content.get("prefix", "")
    content_type = content.get("type", "")
    if content_type == "Series":
        is_episode = True
    else:
        is_episode = False
    content_categories = content.get("contentCategories")
    matching_category_id = None
    for this_cat in content_categories:
        this_cat_name = this_cat.get("category", {}).get("name")
        this_cat_id = this_cat.get("id")
        this_cat_template_id = this_cat.get("category", {}).get("id")
        if this_cat_name == category_name:
            matching_category_id = this_cat_id
            break

    # check to see if this category is already subscribed to partner
    url = "{0}/reachengine/api/abc/partners/subscriptions/{1}/{2}".format(
        reach_fqdn, content_id, this_cat_template_id
    )
    resp = requests.get(url=url, headers=reach_token)

    if resp.status_code == requests.codes.ok:
        current_sub_list = resp.json()
    else:
        current_sub_list = []

    is_already_added = False
    for this_current_sub in current_sub_list:
        this_current_sub_partner = this_current_sub.get("partner", {}).get("name")
        if this_current_sub_partner == partner_name:
            is_already_added = True
            break

    return is_already_added


def reach_subscribe_category_name_to_partner_name_given_content_id(
    category_name, partner_name, content_id, preroll_name, reach_token, reach_fqdn
):
    """
    :param category_name: plain text of the category (e.g. "Episode")
    :param partner_name: plain text of the partner
    :param content_id: id of the series/movie
    :param reach_token:
    :param reach_fqdn:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name

    # get content first:
    content = reach_get_content(content_id, reach_token, reach_fqdn)
    content_name = content.get("name", "")
    content_prefix = content.get("prefix", "")
    content_type = content.get("type", "")
    if content_type == "Series":
        is_episode = True
    else:
        is_episode = False
    content_categories = content.get("contentCategories")
    matching_category_id = None
    for this_cat in content_categories:
        this_cat_name = this_cat.get("category", {}).get("name")
        this_cat_id = this_cat.get("id")
        this_cat_template_id = this_cat.get("category", {}).get("id")
        if this_cat_name == category_name:
            matching_category_id = this_cat_id

    if matching_category_id is None:
        logger_service.error(
            "Cannot find category {} in content id {}".format(category_name, content_id)
        )
        raise Exception(
            "Category {} not found in content id {}".format(category_name, content_id)
        )

    # get content categories:
    content_category = reach_get_content_category_instance(
        matching_category_id, reach_token, reach_fqdn
    )
    content_category = content_category["category"]

    # get all partners:
    all_partners = reach_get_all_partners(reach_token, reach_fqdn)
    matching_partner = {}
    matching_partner_id = ""
    for this_partner in all_partners:
        this_partner_name = this_partner.get("name")
        if this_partner_name == partner_name:
            matching_partner = this_partner
            matching_partner_id = this_partner.get("id")

    if matching_partner_id == "" and matching_partner == {}:
        logger_service.error('Partner "{}" not found'.format(partner_name))

    # get due date templates
    due_date_to_add_instance = {}
    partner_due_date_templates = reach_get_due_date_templates_given_partner_name(
        partner_name, reach_token, reach_address
    )
    for this_due_date_template in partner_due_date_templates:
        this_due_date_template_name = this_due_date_template.get("name", "")
        if "(Clean - Mastering)" in this_due_date_template_name:
            if "movie" in category_name.lower() or category_name.lower() == "episode":
                # partner_to_update['dueDateTemplate'] = this_due_date_template_name
                due_date_to_add_instance = this_due_date_template
                break

        if "C3/C7" in this_due_date_template_name:
            if category_name.lower() == "episode c type":
                # partner_to_update['dueDateTemplate'] = this_due_date_template_name
                due_date_to_add_instance = this_due_date_template
                break

    # check to see if this category is already subscribed to partner
    # url = "{0}/reachengine/api/abc/partners/subscriptions/{1}/{2}".format(reach_fqdn, content_id, this_cat_template_id)
    # resp = requests.get(
    #     url=url,
    #     headers=reach_token
    # )
    #
    # if resp.status_code == requests.codes.ok:
    #     current_sub_list = resp.json()
    # else:
    #     current_sub_list = []
    #
    # already_added = False
    # for this_current_sub in current_sub_list:
    #     this_current_sub_partner = this_current_sub.get('partner', {}).get('name')
    #     if this_current_sub_partner == partner_name:
    #         already_added = True
    #         break
    #
    # partner_needs_update = False
    # if already_added:
    #     logger_service.info('Category "{0}" already added to Partner "{1}". Trying to update.'.format(category_name,
    #                                                                                           partner_name, ))
    #     partner_needs_update = True

    # get network, then prerolls:
    network_id = content.get("network", {}).get("id")
    network_extended = reach_get_network_extended(network_id, reach_token, reach_fqdn)
    network_all_prerolls = network_extended.get("preRolls")
    preroll_instance = {}
    if preroll_name != "":
        for this_preroll in network_all_prerolls:
            if this_preroll.get("name").lower() == preroll_name.lower():
                preroll_instance = this_preroll
                break

    # prep the json body
    if preroll_instance == {}:
        subscription_json = {
            "artwork": [],
            "category": content_category,
            "content": content,
            "partner": matching_partner,
            "enabled": True,
        }
    else:
        subscription_json = {
            "artwork": [],
            "category": content_category,
            "content": content,
            "partner": matching_partner,
            "preRoll": preroll_instance,
            "enabled": True,
        }

    if due_date_to_add_instance != {}:
        subscription_json["dueDateTemplate"] = due_date_to_add_instance

    # if DirecTV or Verizon, add small artwork
    if partner_name == "DirecTV" or partner_name == "Verizon TVE":
        logger_service.info(
            "partner is {} - attempting to add artwork to subscription".format(
                partner_name
            )
        )
        all_artwork = content.get("artwork")
        all_media_profiles = reach_get_media_accept_profiles(
            "OTHER", reach_token, reach_fqdn
        )
        artwork_map = ""
        for this_map in all_media_profiles:
            if this_map.get("name") == "JPG 288x432 English (DirecTV & Verizon)":
                artwork_map = this_map
                break

        # if only one artwork, just use it (don't dink around trying to compare)
        if len(all_artwork) == 1:
            logger_service.info("updating the subscription json")
            new_artwork_sub_data = {
                "artwork": all_artwork[0],
                "artworkAcceptProfile": artwork_map,
                "artworkSeasonVolume": is_episode,
            }
            subscription_json["artwork"] = [new_artwork_sub_data]
            logger_service.info(
                "subscription_json = {}".format(json.dumps(subscription_json))
            )
            artwork_added = True
        else:
            for this_art in all_artwork:
                this_art_name = this_art.get("name", "")
                logger_service.info('artwork name is "{}"'.format(this_art_name))
                if (
                    content.get("prefix") is not None
                    and "movie" not in content.get("type", "movie").lower()
                ):
                    temp_keyart_name = content.get("prefix") + " Key Art"
                else:
                    temp_keyart_name = content.get("name") + " Key Art"
                logger_service.info('temp_keyart_name is "{}"'.format(temp_keyart_name))
                artwork_added = False
                if this_art_name == temp_keyart_name:
                    logger_service.info("updating the subscription json")
                    new_artwork_sub_data = {
                        "artwork": this_art,
                        "artworkAcceptProfile": artwork_map,
                        "artworkSeasonVolume": is_episode,
                    }
                    subscription_json["artwork"] = [new_artwork_sub_data]
                    logger_service.info(
                        "subscription_json = {}".format(json.dumps(subscription_json))
                    )
                    artwork_added = True

                if not artwork_added:
                    logger_service.error(
                        "Artwork was not added to subscription for partner {}".format(
                            partner_name
                        )
                    )

    # if Charter, add 2:3 Gracenote artwork
    if partner_name == "Charter" or partner_name == "Charter (via Deluxe)":
        logger_service.info(
            "partner is {} - attempting to add artwork to subscription".format(
                partner_name
            )
        )
        all_artwork = content.get("artwork")
        all_media_profiles = reach_get_media_accept_profiles(
            "OTHER", reach_token, reach_fqdn
        )
        artwork_map = ""
        for this_map in all_media_profiles:
            if this_map.get("name") == "JPG 2:3 Ratio - English (from Gracenote)":
                artwork_map = this_map
                break

        # if only one artwork, just use it
        if len(all_artwork) == 1:
            logger_service.info("updating the subscription json")
            new_artwork_sub_data = {
                "artwork": all_artwork[0],
                "artworkAcceptProfile": artwork_map,
                "artworkSeasonVolume": is_episode,
            }
            subscription_json["artwork"] = [new_artwork_sub_data]
            logger_service.info(
                "subscription_json = {}".format(json.dumps(subscription_json))
            )
            artwork_added = True
        else:
            for this_art in all_artwork:
                this_art_name = this_art.get("name", "")
                logger_service.info('artwork name is "{}"'.format(this_art_name))
                if (
                    content.get("prefix") is not None
                    and "movie" not in content.get("type", "movie").lower()
                ):
                    temp_keyart_name = content.get("prefix") + " Key Art"
                else:
                    temp_keyart_name = content.get("name") + " Key Art"
                logger_service.info('temp_keyart_name is "{}"'.format(temp_keyart_name))
                artwork_added = False
                if this_art_name == temp_keyart_name:
                    logger_service.info("updating the subscription json")
                    new_artwork_sub_data = {
                        "artwork": this_art,
                        "artworkAcceptProfile": artwork_map,
                        "artworkSeasonVolume": is_episode,
                    }
                    subscription_json["artwork"] = [new_artwork_sub_data]
                    logger_service.info(
                        "subscription_json = {}".format(json.dumps(subscription_json))
                    )
                    artwork_added = True

                if not artwork_added:
                    logger_service.error(
                        "Artwork was not added to subscription for partner {}".format(
                            partner_name
                        )
                    )

    # if MVPD Affiliate Group, add 4:3 Gracenote artwork
    if partner_name == "MVPD Affiliate Group":
        logger_service.info(
            "partner is {} - attempting to add artwork to subscription".format(
                partner_name
            )
        )
        all_artwork = content.get("artwork", [])
        all_media_profiles = reach_get_media_accept_profiles(
            "OTHER", reach_token, reach_fqdn
        )
        artwork_map = ""
        for this_map in all_media_profiles:
            if this_map.get("name") == "JPG 4:3 Ratio - English (from Gracenote)":
                artwork_map = this_map
                break

        # if only one artwork, just use it
        if len(all_artwork) == 1:
            logger_service.info("updating the subscription json")
            new_artwork_sub_data = {
                "artwork": all_artwork[0],
                "artworkAcceptProfile": artwork_map,
                "artworkSeasonVolume": is_episode,
            }
            subscription_json["artwork"] = [new_artwork_sub_data]
            logger_service.info(
                "subscription_json = {}".format(json.dumps(subscription_json))
            )
            artwork_added = True
        else:
            for this_art in all_artwork:
                this_art_name = this_art.get("name", "")
                logger_service.info('artwork name is "{}"'.format(this_art_name))
                if (
                    content.get("prefix") is not None
                    and "movie" not in content.get("type", "movie").lower()
                ):
                    temp_keyart_name = content.get("prefix") + " Key Art"
                else:
                    temp_keyart_name = content.get("name") + " Key Art"
                logger_service.info('temp_keyart_name is "{}"'.format(temp_keyart_name))
                artwork_added = False
                if this_art_name == temp_keyart_name:
                    logger_service.info("updating the subscription json")
                    new_artwork_sub_data = {
                        "artwork": this_art,
                        "artworkAcceptProfile": artwork_map,
                        "artworkSeasonVolume": is_episode,
                    }
                    subscription_json["artwork"] = [new_artwork_sub_data]
                    logger_service.info(
                        "subscription_json = {}".format(json.dumps(subscription_json))
                    )
                    artwork_added = True

                if not artwork_added:
                    logger_service.error(
                        "Artwork was not added to subscription for partner {}".format(
                            partner_name
                        )
                    )

    # add it to Reach
    # if not already_added:
    try:
        url = "{0}/reachengine/api/abc/partners/{1}/subscriptions".format(
            reach_fqdn, matching_partner_id
        )
        response = requests.post(url=url, headers=reach_token, json=subscription_json)
        logger_service.info(
            "Adding subscription. Response HTTP {}".format(response.status_code)
        )
        if response.status_code == 400:
            logger_service.error(response.text)
    except requests.exceptions.RequestException:
        logger_service.error(
            "HTTP POST Request failed ()".format(inspect.currentframe().f_code.co_name)
        )
        err_msg = "Error calling Reach API POST ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)


def reach_update_subscription_category_name_to_partner_name_given_content_id(
    category_name, partner_name, content_id, preroll_name, reach_token, reach_fqdn
):
    """
    :param category_name: plain text of the category (e.g. "Episode")
    :param partner_name: plain text of the partner
    :param content_id: id of the series/movie
    :param reach_token:
    :param reach_fqdn:
    :return:
    """
    this_func = inspect.currentframe().f_code.co_name

    # get content first:
    content = reach_get_content(content_id, reach_token, reach_fqdn)
    content_name = content.get("name", "")
    content_prefix = content.get("prefix", "")
    content_type = content.get("type", "")
    if content_type == "Series":
        is_episode = True
    else:
        is_episode = False
    content_categories = content.get("contentCategories")
    matching_category_id = None
    for this_cat in content_categories:
        this_cat_name = this_cat.get("category", {}).get("name")
        this_cat_id = this_cat.get("id")
        this_cat_type_id = this_cat.get("category", {}).get("id")
        this_cat_template_id = this_cat.get("category", {}).get("id")
        if this_cat_name == category_name:
            matching_category_id = this_cat_type_id

    if matching_category_id is None:
        logger_service.error(
            "Cannot find category {} in content id {}".format(category_name, content_id)
        )
        # raise Exception('Category {} not found in content id {}'.format(category_name, content_id))

    # get current subscription
    url = "{0}/reachengine/api/abc/partners/subscriptions/{1}/{2}/searches".format(
        reach_fqdn, content_id, matching_category_id
    )
    search_json = {
        "includeEnabled": True,
        "includeDisabled": False,
        "viewTab": ["subscriptions"],
        "videoRole": ["260"],
    }
    response = requests.post(url=url, headers=reach_token, json=search_json)
    if response.status_code == requests.codes.ok:
        curr_subs_json = response.json()
    else:
        curr_subs_json = []

    # get content categories: NEED?
    # content_category = reach_get_content_category_instance(matching_category_id, reach_token, reach_fqdn)
    # content_category = content_category['category']

    # get all partners:
    all_partners = reach_get_all_partners(reach_token, reach_fqdn)
    matching_partner = {}
    matching_partner_id = ""
    for this_partner in all_partners:
        this_partner_name = this_partner.get("name")
        if this_partner_name == partner_name:
            matching_partner = this_partner
            matching_partner_id = this_partner.get("id")

    if matching_partner_id == "" and matching_partner == {}:
        logger_service.error('Partner "{}" not found'.format(partner_name))

    partner_to_update = {}
    for this_curr_sub in curr_subs_json:
        this_sub_partner_id = this_curr_sub.get("partner", {}).get("id", "")
        if this_sub_partner_id == matching_partner_id:
            partner_to_update = this_curr_sub
            break

    if partner_to_update == {}:
        logger_service.error("Problems trying to match current subscription")
        return

    # partner_to_update is the JSON of the current partner settings. Check it and update as needed, then post it back

    # CHECK PREROLL
    if preroll_name != "":
        if partner_to_update.get("preRoll", "") != "":
            logger_service.info(
                "Subscription to {0} already has preroll - skipping".format(
                    partner_name
                )
            )
        else:
            logger_service.info("{}: trying to update preroll".format(partner_name))
            # get network, then network prerolls
            network_id = content.get("network", {}).get("id")
            network_extended = reach_get_network_extended(
                network_id, reach_token, reach_fqdn
            )
            network_all_prerolls = network_extended.get("preRolls")
            preroll_instance = {}

            for this_preroll in network_all_prerolls:
                if this_preroll.get("name").lower() == preroll_name.lower():
                    preroll_instance = this_preroll
                    break
            if preroll_instance != {}:
                partner_to_update["preRoll"] = preroll_instance

    # CHECK DUE DATE TEMPLATE
    if partner_to_update.get("dueDateTemplate", "") != "":
        logger_service.info(
            "Subscription to {0} already has Due Date template - skipping".format(
                partner_name
            )
        )
    else:
        logger_service.info(
            "{}: checking if we need to add due date template".format(partner_name)
        )
        # get due date templates (only adding them if template name contains "(Clean - Mastering)"
        partner_due_date_templates = reach_get_due_date_templates_given_partner_name(
            partner_name, reach_token, reach_address
        )
        for this_due_date_template in partner_due_date_templates:
            this_due_date_template_name = this_due_date_template.get("name", "")
            if "(Clean - Mastering)" in this_due_date_template_name:
                if (
                    "movie" in category_name.lower()
                    or category_name.lower() == "episode"
                ):
                    partner_to_update["dueDateTemplate"] = this_due_date_template
                    break

            if "C3/C7" in this_due_date_template_name:
                if category_name.lower() == "episode c type":
                    partner_to_update["dueDateTemplate"] = this_due_date_template
                    break

    # CHECK ARTWORK
    if partner_to_update.get("artwork", []) != []:
        logger_service.info(
            "Subscription to {0} already has artwork - skipping".format(partner_name)
        )
    else:
        # if DirecTV or Verizon, add artwork
        if partner_name == "DirecTV" or partner_name == "Verizon TVE":
            # logger_service.info('partner is {} - checking artwork'.format(partner_name))
            logger_service.info(
                "partner is {} - attempting to add artwork to subscription".format(
                    partner_name
                )
            )
            all_artwork = content.get("artwork")
            # NOTE: ARTWORK is media profile "OTHER", not "ARTWORK". Also, Artwork is different than an Image.
            all_media_profiles = reach_get_media_accept_profiles(
                "OTHER", reach_token, reach_fqdn
            )
            artwork_map = ""
            for this_map in all_media_profiles:
                if this_map.get("name") == "JPG 288x432 English (DirecTV & Verizon)":
                    artwork_map = this_map
                    break

            # logger_service.info('artwork = {}'.format(json.dumps(all_artwork)))
            for this_art in all_artwork:
                this_art_name = this_art.get("name", "")
                logger_service.info('artwork name is "{}"'.format(this_art_name))
                if (
                    content.get("prefix") is not None
                    and "movie" not in content.get("type", "movie").lower()
                ):
                    temp_keyart_name = content.get("prefix") + " Key Art"
                else:
                    temp_keyart_name = content.get("name") + " Key Art"
                logger_service.info('temp_keyart_name is "{}"'.format(temp_keyart_name))
                # artwork_added = False
                if this_art_name == temp_keyart_name:
                    logger_service.info("updating the subscription json")
                    new_artwork_sub_data = {
                        "artwork": this_art,
                        "artworkAcceptProfile": artwork_map,
                        "artworkSeasonVolume": is_episode,
                    }
                    partner_to_update["artwork"] = [new_artwork_sub_data]

        # if Charter, add 2:3 Gracenote artwork
        if partner_name == "Charter" or partner_name == "Charter (via Deluxe)":
            logger_service.info(
                "partner is {} - attempting to add artwork to subscription".format(
                    partner_name
                )
            )
            all_artwork = content.get("artwork")
            all_media_profiles = reach_get_media_accept_profiles(
                "OTHER", reach_token, reach_fqdn
            )
            artwork_map = ""
            for this_map in all_media_profiles:
                if this_map.get("name") == "JPG 2:3 Ratio - English (from Gracenote)":
                    artwork_map = this_map
                    break

            # logger_service.info('artwork = {}'.format(json.dumps(all_artwork)))
            for this_art in all_artwork:
                this_art_name = this_art.get("name", "")
                logger_service.info('artwork name is "{}"'.format(this_art_name))
                if (
                    content.get("prefix") is not None
                    and "movie" not in content.get("type", "movie").lower()
                ):
                    temp_keyart_name = content.get("prefix") + " Key Art"
                else:
                    temp_keyart_name = content.get("name") + " Key Art"
                logger_service.info('temp_keyart_name is "{}"'.format(temp_keyart_name))
                # artwork_added = False
                if this_art_name == temp_keyart_name:
                    logger_service.info("updating the subscription json")
                    new_artwork_sub_data = {
                        "artwork": this_art,
                        "artworkAcceptProfile": artwork_map,
                        "artworkSeasonVolume": is_episode,
                    }
                    partner_to_update["artwork"] = [new_artwork_sub_data]

        # if MVPD Affiliate Group, add 4:3 Gracenote artwork
        if partner_name == "MVPD Affiliate Group":
            logger_service.info(
                "partner is {} - attempting to add artwork to subscription".format(
                    partner_name
                )
            )
            all_artwork = content.get("artwork", [])
            all_media_profiles = reach_get_media_accept_profiles(
                "OTHER", reach_token, reach_fqdn
            )
            artwork_map = ""
            for this_map in all_media_profiles:
                if this_map.get("name") == "JPG 4:3 Ratio - English (from Gracenote)":
                    artwork_map = this_map
                    break

            # if only one artwork, just use it
            if len(all_artwork) == 1:
                logger_service.info("updating the subscription json")
                new_artwork_sub_data = {
                    "artwork": all_artwork[0],
                    "artworkAcceptProfile": artwork_map,
                    "artworkSeasonVolume": is_episode,
                }
                partner_to_update["artwork"] = [new_artwork_sub_data]
                # logger_service.info('subscription_json = {}'.format(json.dumps(subscription_json)))
                # artwork_added = True
            else:
                for this_art in all_artwork:
                    this_art_name = this_art.get("name", "")
                    logger_service.info('artwork name is "{}"'.format(this_art_name))
                    if (
                        content.get("prefix") is not None
                        and "movie" not in content.get("type", "movie").lower()
                    ):
                        temp_keyart_name = content.get("prefix") + " Key Art"
                    else:
                        temp_keyart_name = content.get("name") + " Key Art"
                    logger_service.info(
                        'temp_keyart_name is "{}"'.format(temp_keyart_name)
                    )
                    artwork_added = False
                    if this_art_name == temp_keyart_name:
                        logger_service.info("updating the subscription json")
                        new_artwork_sub_data = {
                            "artwork": this_art,
                            "artworkAcceptProfile": artwork_map,
                            "artworkSeasonVolume": is_episode,
                        }
                        partner_to_update["artwork"] = [new_artwork_sub_data]
                        # logger_service.info('subscription_json = {}'.format(json.dumps(subscription_json)))
                        # artwork_added = True

                    # if not artwork_added:
                    #     logger_service.error('Artwork was not added to subscription for partner {}'.format(partner_name))

    # update it in Reach
    try:
        url = "{0}/reachengine/api/abc/partners/subscriptions".format(reach_fqdn)
        response = requests.post(url=url, headers=reach_token, json=[partner_to_update])
        logger_service.info(
            "Updating subscription. Response HTTP {}".format(response.status_code)
        )
        if response.status_code == 400:
            logger_service.error(response.text)
    except requests.exceptions.RequestException:
        logger_service.error(
            "HTTP POST Request failed ()".format(inspect.currentframe().f_code.co_name)
        )
        err_msg = "Error calling Reach API POST ({})".format(url)
        logger_service.error(err_msg)
        raise ConnectionError(err_msg)


def partner_get_dist_grid_anet(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param c_length: how many days in the C window (3 or 7)
    :param is_acquired: should we apply the "acquired" rules (e.g. Clean gets Day 1 instead of Day 4/8)
    :param pkg_category: Episode or Episode C-Type or Movie
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "aNETLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "aNETExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So we'll move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    partner_json["metadata"]["properties"]["aNETLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["aNETExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_charter(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param is_acquired:
    :param c_length: how many days in the C window (3 or 7)
    :param pkg_category:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "charterLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "charterExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    partner_json["metadata"]["properties"]["charterLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["charterExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_comcast(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param is_acquired:
    :param pkg_category:
    :param c_length:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "comcastSTBLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "comcastSTBExpire": {"value": "9999-01-01T00:00:00.000Z"},
                "comcastTVEFreeLaunch": {"value": "9999-01-01T00:00:00.000+0000"},
                "comcastTVEFreeExpire": {"value": "9999-01-01T00:00:00.000+0000"},
                "comcastTVEPaidLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "comcastTVEPaidExpire": {"value": "9999-01-01T00:00:00.000Z"},
                "comcastTVEStackedLaunch": {"value": "9999-01-01T00:00:00.000+0000"},
                "comcastTVEStackedExpire": {"value": "9999-01-01T00:00:00.000+0000"},
            }
        },
    }
    window_start_plus_c = window_start  # default value - can override later
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start_plus_c = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    # STB window - only adjust STB
    partner_json["metadata"]["properties"]["comcastSTBLaunch"][
        "value"
    ] = window_start_plus_c
    partner_json["metadata"]["properties"]["comcastSTBExpire"]["value"] = window_end

    # TVE window (.meta) - Comcast gets Clean on Day 1. No adjustments necessary.
    # C Types do not go to TVE
    if pkg_category.lower() != "episode c type":
        partner_json["metadata"]["properties"]["comcastTVEPaidLaunch"][
            "value"
        ] = window_start
        partner_json["metadata"]["properties"]["comcastTVEPaidExpire"][
            "value"
        ] = window_end

    return partner_json


def partner_get_dist_grid_directv(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z

    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "directTVLaunch": {"value": "9999-01-01T00:00:00.000+0000"},
                "directTVExpire": {"value": "9999-01-01T00:00:00.000+0000"},
            }
        },
    }
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            # window_start = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z") # this format ends with 000000+0000
            # 000000+0000 will not work with Reach
            window_start = new_start.isoformat(
                timespec="milliseconds"
            )  # will end with 000+00:00 - this works

    partner_json["metadata"]["properties"]["directTVLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["directTVExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_dish(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param is_acquired:
    :param c_length:
    :param pkg_category:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z

    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "dISHSTBLaunch": {"value": "3000-01-01T08:00:00.000+0000"},
                "dISHSTBExpire": {"value": "3000-01-01T08:00:00.000+0000"},
                "dishLaunch": {"value": "3000-01-01T08:00:00.000+0000"},
                "dishExpire": {"value": "3000-01-01T08:00:00.000+0000"},
            }
        },
    }
    window_start_plus_c = (
        window_start  # default window start value - can override later
    )
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start_plus_c = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    # STB dates
    partner_json["metadata"]["properties"]["dISHSTBLaunch"][
        "value"
    ] = window_start_plus_c
    partner_json["metadata"]["properties"]["dISHSTBExpire"]["value"] = window_end

    if pkg_category.lower() != "episode c type":
        partner_json["metadata"]["properties"]["dishLaunch"]["value"] = window_start
        partner_json["metadata"]["properties"]["dishExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_dtci(
    partner_instance_id, window_start, window_end, free_window_start, free_window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param free_window_end:
    :param free_window_start:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z

    # Note: yes we are aware there's a typo in this property name "subscripton" vs "subscription".
    # This is the property name in Reach
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "dATGSubscriptonLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "dATGSubscriptonExpire": {"value": "9999-01-01T00:00:00.000Z"},
                "dATGLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "dATGExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    # sites/apps gets clean on Day 1
    if window_start.startswith("3000") or window_end.startswith("3000"):
        del partner_json["metadata"]["properties"]["dATGSubscriptonLaunch"]
        del partner_json["metadata"]["properties"]["dATGSubscriptonExpire"]
    else:
        partner_json["metadata"]["properties"]["dATGSubscriptonLaunch"][
            "value"
        ] = window_start
        partner_json["metadata"]["properties"]["dATGSubscriptonExpire"][
            "value"
        ] = window_end

    if free_window_start.startswith("3000") or free_window_end.startswith("3000"):
        del partner_json["metadata"]["properties"]["dATGLaunch"]
        del partner_json["metadata"]["properties"]["dATGExpire"]
    else:
        partner_json["metadata"]["properties"]["dATGLaunch"][
            "value"
        ] = free_window_start
        partner_json["metadata"]["properties"]["dATGExpire"]["value"] = free_window_end

    return partner_json


def partner_get_dist_grid_fubotv(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param is_acquired:
    :param c_length:
    :param pkg_category:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z

    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "fuboTVLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "fuboTVExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    partner_json["metadata"]["properties"]["fuboTVLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["fuboTVExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_hulu(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "huluLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "huluExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }

    partner_json["metadata"]["properties"]["huluLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["huluExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_hulu_dmvpd(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param is_acquired:
    :param pkg_category:
    :param c_length:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "huluDMVPDLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "huluDMVPDExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    partner_json["metadata"]["properties"]["huluDMVPDLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["huluDMVPDExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_verizon(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "verizonVODLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "verizonVODExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    # Verizon gets clean on Day 1 (no C3/C7)
    partner_json["metadata"]["properties"]["verizonVODLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["verizonVODExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_vidgo(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    #   VIDGO IS PRETTY MUCH DEAD  May 2024 R.I.P.
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z

    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "vidgoLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "vidgoExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    partner_json["metadata"]["properties"]["vidgoLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["vidgoExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_youtube_dmvpd(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param is_acquired:
    :param c_length:
    :param pkg_category:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "youTubeLaunch": {"value": "9999-01-01T00:00:00.000Z"},
                "youTubeExpire": {"value": "9999-01-01T00:00:00.000Z"},
            }
        },
    }
    # YouTube gets Clean on Day 1 (no C3/C7)
    partner_json["metadata"]["properties"]["youTubeLaunch"]["value"] = window_start
    partner_json["metadata"]["properties"]["youTubeExpire"]["value"] = window_end

    return partner_json


def partner_get_dist_grid_dmvpd_affiliate_group(
    partner_instance_id, pkg_category, c_length, is_acquired, window_start, window_end
):
    """
    Takes dates and a partner instance id, generate the payload to update a partner in a package Distribution Grid
    :param is_acquired:
    :param c_length: how many days in the C window (3 or 7)
    :param pkg_category:
    :param partner_instance_id:
    :param window_start:
    :param window_end:
    :return:
    """

    # dates should be ISO8601 - example: 9999-01-01T00:00:00.000Z ("None") or 2024-02-27T08:00:00.000Z
    partner_json = {
        "id": partner_instance_id,
        "metadata": {
            "properties": {
                "affiliateSTBSunrise": {"value": "9999-01-01T00:00:00.000Z"},
                "affiliateSTBSunset": {"value": "9999-01-01T00:00:00.000Z"},
                "affiliateDeliveryList": {"value": []},
            }
        },
    }
    if not is_acquired:  # is not acquired - check if this is Clean or C Type
        if (
            pkg_category.lower() == "episode"
        ):  # Clean - probably has a matching C Type. So move Clean windows ahead
            start_dt = datetime.strptime(window_start, "%Y-%m-%dT%H:%M:%S.%f%z")
            c_window_delta = timedelta(days=c_length)
            new_start = start_dt + c_window_delta
            window_start = datetime.strftime(new_start, "%Y-%m-%dT%H:%M:%S.%f%z")

    partner_json["metadata"]["properties"]["affiliateSTBSunrise"][
        "value"
    ] = window_start
    partner_json["metadata"]["properties"]["affiliateSTBSunset"]["value"] = window_end

    # these are for Staging. Prod will have different values.
    # this lookup table can be found here (API call): https://staging8.reachengine.disney.com/reachengine/api/abc/metadata/properties
    # or here: https://reachengine.disney.com/reachengine/api/abc/metadata/properties
    # affiliate_list = [
    #         "caa73bc3-fb01-4952-a74a-ad2ee64097d6",  # Altice
    #         "ecfdfb93-f121-4a34-8b30-c0dcaba18430",  # Cox
    #         "583bb6b1-f333-4677-a6b1-6d2a71eb1085",  # MobiTV
    #         "2f0c1544-aa5b-492a-9484-a4df0e7f5193",  # Verizon STB
    #         "c1b9d6f3-a4ce-4ed0-917b-eec5bdc97e35",  # Verizon Lab
    #         "fb03795a-f340-4d46-9ddd-20d391ac8849",  # ASCP_TEST
    #         "c67c9486-843f-470e-86dd-75310ffa85fc",  # FASPEX_TEST
    #         "10d604a4-d84e-46da-9b0a-1e8b5b19fff2"   # Tangerine (Allo)
    #       ]

    # Prod affiliate list:
    # taken from https://reachengine.disney.com/reachengine/api/abc/partners/3520/metadataProperties?mvpd=false
    prod_affiliate_list = [
        {"id": "6f4594fd-b33b-4e0c-a49c-79da88a03a03", "displayName": "Cox"},
        {"id": "e0a1309a-2f95-45b4-88c1-0afc969456ad", "displayName": "MobiTV"},
        {
            "id": "3dd2ac25-19ef-4445-9345-02caf031b3ed",
            "displayName": "Tangerine (Allo)",
        },
        {"id": "e5ec58f3-0066-49cd-962f-6a506c2f9c48", "displayName": "Verizon STB"},
        {
            "id": "d6f74ccb-7205-4197-aa03-2bac64f6960b",
            "displayName": "Cablevision (Altice)",
        },
        {"id": "e716f803-4c2c-41e3-8578-bc1b1fc49453", "displayName": "Alta Fiber"},
        {"id": "aa069cb7-17e9-4cd1-aa31-7f20ce0a5f66", "displayName": "Verizon Lab"},
        {"id": "19b4b081-66e4-4fad-b769-0b3f816bdf10", "displayName": "Vubiquity WOW"},
        {"id": "2d773d9b-fa47-4c97-96e5-a5c1c6388e33", "displayName": "Antietam"},
        {"id": "a5d3a5c6-fd74-4807-b156-e2c6476ca150", "displayName": "Astound (RCN)"},
        {"id": "949bf8b9-138e-4732-8003-a2e3b1f5de89", "displayName": "CSI Digital"},
        {"id": "91794e8f-0a64-4475-89a3-fcb9b785c5d0", "displayName": "HBC"},
        {"id": "34d01172-f1ee-4258-87cd-d65eab09048b", "displayName": "OzarksGo"},
        {
            "id": "84d22d7b-afad-4676-9d9a-686fd9822dab",
            "displayName": "Vermont Telephone",
        },
        {"id": "5fd70c68-d264-4803-9c0f-be20105a3e58", "displayName": "Armstrong"},
        {"id": "1820278f-0f2b-46df-9e4f-95cc8f8c88b9", "displayName": "Breezeline"},
        {"id": "e70316e9-57b5-41c1-bcf6-164b27f8fb09", "displayName": "Blue Ridge"},
        {
            "id": "c7714495-b7a6-4584-b00d-75aa5460ea96",
            "displayName": "BlueStream Fiber",
        },
        {
            "id": "89156427-4268-4b21-ad21-9fdcacc2369c",
            "displayName": "Cinergy Metronet",
        },
        {"id": "3b097738-e5c0-4794-8539-13cb849a1d67", "displayName": "C-Spire"},
        {"id": "380af74d-da7d-4080-b78d-5c9b8c2aa4bc", "displayName": "Buckeye"},
        {"id": "525d7bd2-a264-4b28-9608-ec443af7d4e0", "displayName": "EPB"},
        {"id": "c2ab9c1d-4fba-4cb2-b1cc-c96e77d54fe0", "displayName": "GCI"},
        {"id": "b1e826c3-6716-46ac-a96a-f1ce753597e3", "displayName": "Hotwire"},
        {"id": "f55a9259-a204-4c4e-b55c-6a84e09ff19d", "displayName": "MCTV"},
        {"id": "810d1eba-ae4a-44aa-afd1-2ad94b9c3d01", "displayName": "Mediacom"},
        {
            "id": "d9c2f256-dcc7-44be-908c-1d2365d22fc9",
            "displayName": "SECTV LehighValley",
        },
        {
            "id": "90fae7da-73de-4c11-9860-1c08b4c29eab",
            "displayName": "SECTV Sparta NJ",
        },
        {"id": "f655b9dc-562e-43d5-9c47-3854db361698", "displayName": "SECTV"},
        {"id": "ce34bfac-f87a-4b0f-8f24-eee1af2a487d", "displayName": "TDS Broadband"},
        {"id": "cbc684c0-8655-4b79-b1ec-c27a7c0225d1", "displayName": "Bend Broadband"},
        {
            "id": "00120894-a048-445c-b14d-94a621811128",
            "displayName": "Citizens Cable Comms",
        },
    ]

    affiliate_list = []
    for aff in prod_affiliate_list:
        affiliate_list.append(aff.get("id", ""))

    partner_json["metadata"]["properties"]["affiliateDeliveryList"][
        "value"
    ] = affiliate_list

    return partner_json


def affiliate_generate_mvpd_default_values(content_json):
    aff_data = {
        "provider": "",
        "product_code": "",
        "pct": "",
        "category_mapping_list": [],
        "category_mapping_list_hd": [],
        "genre_list": [],
    }

    # PROVIDER
    affiliate_network = content_json.get("network", {}).get("value", "")
    if affiliate_network == "FX":
        aff_data["provider"] = "FX_HD"
    elif affiliate_network == "FXX":
        aff_data["provider"] = "FXX_HD"
    elif affiliate_network == "FXM":
        aff_data["provider"] = "FXM_HD"
    elif affiliate_network == "NG":
        aff_data["provider"] = "NATGEO_HD"
    elif affiliate_network == "NGW":
        aff_data["provider"] = "NATGEOWILD_HD"

    # PRODUCT CODE
    aff_data["product_code"] = "MOD"

    # PRODUCT CONTENT TIER - PCT
    if affiliate_network == "FX":
        aff_data["pct"] = "FX_PRIMETIME_FF_HD"
    elif affiliate_network == "FXX":
        aff_data["pct"] = (
            "FXX"  # TODO: might be different. Need more info from affiliates
        )
    elif affiliate_network == "FXM":
        aff_data["pct"] = "FXM"  # TODO:
    elif affiliate_network == "NG":
        aff_data["pct"] = "NATIONALGEOGRAPHIC_HD_5"
    elif affiliate_network == "NGW":
        aff_data["pct"] = "NAT_GEO_WILD_HD"

    # CATEGORY MAPPINGS
    affiliate_category_mappings = content_json.get("sd_mappings", {}).get(
        "value", []
    )  # this is a list
    aff_data["category_mapping_list"] = affiliate_category_mappings
    affiliate_category_mappings_hd = content_json.get("hd_mappings", {}).get(
        "value", []
    )  # this is a list
    aff_data["category_mapping_list_hd"] = affiliate_category_mappings_hd

    # GENRE
    affiliate_genre = content_json.get("genre_code", {}).get("value", [])
    aff_data["genre_list"] = affiliate_category_mappings

    return aff_data


def affiliate_generate_mvpd_values_alta_fiber(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### All Mappings ####
    replacement_mapping = []
    replacement_mapping.append(
        "Free On Demand/By Channel/" + affiliate_network + "/" + content_name
    )

    aff_data["category_mapping_list_hd"] = replacement_mapping
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_altice(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    if affiliate_network == "FX":
        pass  # keep defaults
    elif affiliate_network == "FXX":
        pass  # keep defaults
    elif affiliate_network == "FXM":
        pass  # keep defaults
    elif affiliate_network == "National Geographic":
        affiliate_network = "NatGeo"
    elif affiliate_network == "Nat Geo Wild":
        affiliate_network = "NatGeoWild"

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # generate network mapping
    replacement_mapping = []
    replacement_mapping.append(
        affiliate_network + "/" + content_name
    )  # example: "FX/Fargo"

    aff_data["category_mapping_list_hd"] = replacement_mapping
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_armstrong(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    if affiliate_network == "National Geographic":
        affiliate_network = "Nat Geo"

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_first_letter = content_name.strip()[:1]
    showname_range = armstrong_get_alpha_range(content_first_letter)

    # #### All Mappings ####
    replacement_mapping = []
    replacement_mapping.append("TV Network/" + affiliate_network + "/" + content_name)
    replacement_mapping.append(
        "TV Series/Series " + showname_range + "/" + content_name
    )

    aff_data["category_mapping_list_hd"] = replacement_mapping
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_bluestream(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)
    if "fx" in affiliate_network.lower():
        network_path = "D-F"
    elif "national" in affiliate_network.lower() or "wild" in affiliate_network.lower():
        network_path = "J-N"
    else:
        logger_service.error(
            "Unknown network for Bluestream: {}".format(affiliate_network)
        )
        network_path = "D-F"

    # #### HD Mappings ####
    # example: "TV/Cable Networks J-N/Nat Geo Wild/Goas SOS Whatever"
    replacement_mapping = [
        "TV/Cable Networks "
        + network_path
        + "/"
        + affiliate_network
        + "/"
        + content_name
    ]
    # if is_movie:
    #     replacement_mapping.append('Free TV Shows/' + affiliate_network + '/Movies')
    #     replacement_mapping.append('Movies/Free Movies/' + affiliate_network)
    # else:
    #     replacement_mapping.append('Free TV Shows/' + affiliate_network + '/' + content_name)

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    # replacement_mapping = []
    # if is_movie:
    #     replacement_mapping.append('Free TV Shows/' + affiliate_network + '/Movies')
    #     replacement_mapping.append('Movies/Free Movies/' + affiliate_network)
    # else:
    #     replacement_mapping.append('Free TV Shows/' + affiliate_network + '/' + content_name)

    # only need to worry about HD, but we'll add it here just in case
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_breezeline(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Free TV Shows/" + affiliate_network + "/Movies")
        replacement_mapping.append("Movies/Free Movies/" + affiliate_network)
    else:
        replacement_mapping.append(
            "Free TV Shows/" + affiliate_network + "/" + content_name
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Free TV Shows/" + affiliate_network + "/Movies")
        replacement_mapping.append("Movies/Free Movies/" + affiliate_network)
    else:
        replacement_mapping.append(
            "Free TV Shows/" + affiliate_network + "/" + content_name
        )

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_buckeye(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("TV Entertainment/" + affiliate_network + "/Movies")
    else:
        replacement_mapping.append(
            "TV Entertainment/" + affiliate_network + "/" + content_name
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("TV Entertainment/" + affiliate_network + "/Movies")
    else:
        replacement_mapping.append(
            "TV Entertainment/" + affiliate_network + "/" + content_name
        )

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_cspire(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### All Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Movies/Free Movies/" + affiliate_network)
    else:
        replacement_mapping.append(
            "TV Shows/By Network/" + affiliate_network + "/" + content_name + " HD"
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping
    aff_data["category_mapping_list"] = (
        replacement_mapping  # won't be used but put here to override defaults
    )

    return aff_data


def affiliate_generate_mvpd_values_cox(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    if affiliate_network == "FX":
        aff_data["provider"] = "MP_FXHD"
    elif affiliate_network == "FXX":
        pass  # keep defaults
    elif affiliate_network == "FXM":
        pass  # keep defaults
    elif affiliate_network == "National Geographic":
        aff_data["provider"] = "NAT_GEOHD"
    elif affiliate_network == "Nat Geo Wild":
        aff_data["provider"] = "NAT_GEO_WILD_HD"

    # override Product Code for some networks
    if affiliate_network == "FX":
        aff_data["product_code"] = "MPT"
    elif affiliate_network == "FXX":
        aff_data["product_code"] = "FZHD"
    elif affiliate_network == "FXM":
        pass  # keep defaults
    elif affiliate_network == "National Geographic":
        aff_data["product_code"] = "FZHD"
    elif affiliate_network == "Nat Geo Wild":
        aff_data["product_code"] = "FZHD"

    # override PCT for some networks
    if affiliate_network == "FX":
        aff_data["pct"] = "FX_PRIMETIME_FF_C3"
    elif affiliate_network == "FXX":
        pass  # keep default
    elif affiliate_network == "FXM":
        pass  # keep default
    elif affiliate_network == "National Geographic":
        pass  # keep default
    elif affiliate_network == "Nat Geo Wild":
        aff_data["pct"] = "NATIONALGEOGRAPHIC_EXPANDED_HD"

    # Cox category mappings - two types:
    # Category based on network:             TV/TV Networks/Networks D-G/FX/Welcome Wrexham HD
    # Category based on show name (series):  TV/TV Shows/V-Z/Welcome Wrexham HD
    # Category based on show name (movie):   Movies/Network Movies/FX Movies

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_first_letter = content_name.strip()[:1]
    showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD ####
    # generate HD network mapping
    replacement_mapping = []
    if affiliate_network == "FX":
        replacement_mapping.append(
            "TV/TV Networks/Networks D-G/FX/" + content_name + " HD"
        )
    elif affiliate_network == "FXX":
        replacement_mapping.append(
            "TV/TV Networks/Networks D-G/FXX/" + content_name + " HD"
        )
    elif affiliate_network == "FXM":
        replacement_mapping.append("Movies/Network Movies/FXM/FXM Blockbusters")
    elif affiliate_network == "National Geographic":
        replacement_mapping.append(
            "TV/TV Networks/Networks N-S/Nat Geo/" + content_name + " HD"
        )
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append(
            "TV/TV Networks/Networks N-S/Nat Geo Wild/" + content_name + " HD"
        )

    # generate HD show mapping
    if is_movie:
        if affiliate_network == "FX":
            replacement_mapping.append("Movies/Network Movies/FX Movies")
        elif affiliate_network == "FXX":
            replacement_mapping.append(
                "Movies/Network Movies/FXX Movies"
            )  # not sure if this is correct
        elif affiliate_network == "FXM":
            pass  # category already added at network level above - don't need a 2nd one for Cox
        elif affiliate_network == "National Geographic":
            replacement_mapping.append(
                "Movies/Network Movies/NatGeo Movies"
            )  # probably will never happen
        elif affiliate_network == "Nat Geo Wild":
            replacement_mapping.append(
                "Movies/Network Movies/NatGeo Wild Movies"
            )  # probably will never happen
    else:
        if affiliate_network == "FX":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name + " HD"
            )
        elif affiliate_network == "FXX":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name + " HD"
            )
        elif affiliate_network == "FXM":
            pass  # should not have this if not is_movie
        elif affiliate_network == "National Geographic":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name + " HD"
            )
        elif affiliate_network == "Nat Geo Wild":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name + " HD"
            )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # ###  SD  ###
    # generate SD network mapping
    replacement_mapping = []
    if affiliate_network == "FX":
        replacement_mapping.append("TV/TV Networks/Networks D-G/FX/" + content_name)
    elif affiliate_network == "FXX":
        replacement_mapping.append("TV/TV Networks/Networks D-G/FXX/" + content_name)
    elif affiliate_network == "FXM":
        replacement_mapping.append("Movies/Network Movies/FXM/FXM Blockbusters")
    elif affiliate_network == "National Geographic":
        replacement_mapping.append(
            "TV/TV Networks/Networks N-S/Nat Geo/" + content_name
        )
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append(
            "TV/TV Networks/Networks N-S/Nat Geo Wild/" + content_name
        )

    # generate SD show mapping
    if is_movie:
        if affiliate_network == "FX":
            replacement_mapping.append("Movies/Network Movies/FX Movies")
        elif affiliate_network == "FXX":
            replacement_mapping.append(
                "Movies/Network Movies/FXX Movies"
            )  # not sure if this is correct
        elif affiliate_network == "FXM":
            pass  # category already added at network level above - don't need a 2nd one for Cox
        elif affiliate_network == "National Geographic":
            replacement_mapping.append(
                "Movies/Network Movies/NatGeo Movies"
            )  # probably will never happen
        elif affiliate_network == "Nat Geo Wild":
            replacement_mapping.append(
                "Movies/Network Movies/NatGeo Wild Movies"
            )  # probably will never happen
    else:
        if affiliate_network == "FX":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name
            )
        elif affiliate_network == "FXX":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name
            )
        elif affiliate_network == "FXM":
            pass  # should not have this if not is_movie
        elif affiliate_network == "National Geographic":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name
            )
        elif affiliate_network == "Nat Geo Wild":
            replacement_mapping.append(
                "TV/TV Shows/" + showname_range + "/" + content_name
            )

        aff_data["category_mapping_list"] = replacement_mapping
    return aff_data


def affiliate_generate_mvpd_values_epb(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Movies/Free Movies")
        replacement_mapping.append("TV/Networks/" + affiliate_network + "/Movies")
    else:
        replacement_mapping.append(
            "TV/Networks/" + affiliate_network + "/" + content_name + " HD"
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Movies/Free Movies")
        replacement_mapping.append("TV/Networks/" + affiliate_network + "/Movies")
    else:
        replacement_mapping.append(
            "TV/Networks/" + affiliate_network + "/" + content_name
        )
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_frontier(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    # if affiliate_network == 'FX':
    #     aff_data['provider'] = 'MP_FXHD'
    # elif affiliate_network == 'FXX':
    #     pass  # keep defaults
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['provider'] = 'NAT_GEOHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['provider'] = 'NAT_GEO_WILD_HD'
    #
    # # override Product Code for some networks
    # if affiliate_network == 'FX':
    #     aff_data['product_code'] = 'MPT'
    # elif affiliate_network == 'FXX':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['product_code'] = 'FZHD'
    #
    # # override PCT for some networks
    # if affiliate_network == 'FX':
    #     aff_data['pct'] = 'FX_PRIMETIME_FF_C3'
    # elif affiliate_network == 'FXX':
    #     pass # keep default
    # elif affiliate_network == 'FXM':
    #     pass  # keep default
    # elif affiliate_network == 'National Geographic':
    #     pass  # keep default
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['pct'] = 'NATIONALGEOGRAPHIC_EXPANDED_HD'

    # Frontier category mappings - multiple types:
    # Cat based on genre:               Free & Premium/TV Shows/By Genre/Entertainment/FX/FX HD/The NYT Presents
    # Cat based on network:             Free & Premium/TV Shows/By Network/D - G/FX/FX HD/The NYT Presents
    # Cat based on show name (series):  TV Shows/By Genre/Entertainment/FX/FX HD/The NYT Presents
    # Cat based on show name (movie):   TV Shows/By Network/D - G/FX/FX HD/The NYT Presents

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD ####
    # generate HD mapping based on genre
    replacement_mapping = []
    if affiliate_network == "FX":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/FX/FX HD/" + content_name
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/FX/FX HD/" + content_name
        )
    elif affiliate_network == "FXX":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/FXX/FXX HD/" + content_name
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/FXX/FXX HD/" + content_name
        )
    elif affiliate_network == "FXM":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/FXM/FXM HD/" + content_name
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/FXM/FXM HD/" + content_name
        )
    elif affiliate_network == "National Geographic":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/Nat Geo/Nat Geo HD/"
            + content_name
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/Nat Geo/Nat Geo HD/" + content_name
        )
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/Nat Geo Wild/Nat Geo Wild HD/"
            + content_name
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/Nat Geo Wild/Nat Geo Wild HD/"
            + content_name
        )

    # generate HD mapping based on network
    if is_movie:
        pass
        # TODO: we don't have any example movie cats yet
        # if affiliate_network == 'FX':
        #     replacement_mapping.append('Free & Premium/TV Shows/By Network/D - G/FX/FX HD/' + content_name)
        #     replacement_mapping.append('TV Shows/By Network/D - G/FX/FX HD/' + content_name)
        # elif affiliate_network == 'FXX':
        #     replacement_mapping.append('Free & Premium/TV Shows/By Network/D - G/FXX/FXX HD/' + content_name)
        #     replacement_mapping.append('TV Shows/By Network/D - G/FXX/FXX HD/' + content_name)
        # elif affiliate_network == 'FXM':
        #     pass  # category already added at network level above - don't need a 2nd one for Cox
        # elif affiliate_network == 'National Geographic':
        #     replacement_mapping.append('Free & Premium/TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/' + content_name) # ???
        #     replacement_mapping.append('TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/' + content_name)
        # elif affiliate_network == 'Nat Geo Wild':
        #     replacement_mapping.append('Movies/Network Movies/NatGeo Wild Movies')  # probably will never happen
    else:
        if affiliate_network == "FX":
            replacement_mapping.append(
                "Free & Premium/TV Shows/By Network/D - G/FX/FX HD/" + content_name
            )
            replacement_mapping.append(
                "TV Shows/By Network/D - G/FX/FX HD/" + content_name
            )
        elif affiliate_network == "FXX":
            replacement_mapping.append(
                "Free & Premium/TV Shows/By Network/D - G/FXX/FXX HD/" + content_name
            )
            replacement_mapping.append(
                "TV Shows/By Network/D - G/FXX/FXX HD/" + content_name
            )
        elif affiliate_network == "FXM":
            pass  # should not have this if not is_movie
        elif affiliate_network == "National Geographic":
            replacement_mapping.append(
                "Free & Premium/TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/"
                + content_name
            )  # ???
            replacement_mapping.append(
                "TV Shows/By Network/K - N/Nat Geo/Nat Geo HD/" + content_name
            )
        elif affiliate_network == "Nat Geo Wild":
            replacement_mapping.append(
                "Free & Premium/TV Shows/By Network/K - N/Nat Geo Wild/Nat Geo Wild HD/"
                + content_name
            )  # ???
            replacement_mapping.append(
                "TV Shows/By Network/K - N/Nat Geo Wild/Nat Geo Wild HD/" + content_name
            )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # ###  SD  ###
    # generate SD network mapping
    # replacement_mapping = []
    # if affiliate_network == 'FX':
    #     replacement_mapping.append('TV/TV Networks/Networks D-G/FX/' + content_name)
    # elif affiliate_network == 'FXX':
    #     replacement_mapping.append('TV/TV Networks/Networks D-G/FXX/' + content_name)
    # elif affiliate_network == 'FXM':
    #     replacement_mapping.append('Movies/Network Movies/FXM/FXM Blockbusters')
    # elif affiliate_network == 'National Geographic':
    #     replacement_mapping.append('TV/TV Networks/Networks N-S/Nat Geo/' + content_name)
    # elif affiliate_network == 'Nat Geo Wild':
    #     replacement_mapping.append('TV/TV Networks/Networks N-S/Nat Geo Wild/' + content_name)
    #
    # # generate SD show mapping
    # if is_movie:
    #     if affiliate_network == 'FX':
    #         replacement_mapping.append('Movies/Network Movies/FX Movies')
    #     elif affiliate_network == 'FXX':
    #         replacement_mapping.append('Movies/Network Movies/FXX Movies')  # not sure if this is correct
    #     elif affiliate_network == 'FXM':
    #         pass  # category already added at network level above - don't need a 2nd one for Cox
    #     elif affiliate_network == 'National Geographic':
    #         replacement_mapping.append('Movies/Network Movies/NatGeo Movies')  # probably will never happen
    #     elif affiliate_network == 'Nat Geo Wild':
    #         replacement_mapping.append('Movies/Network Movies/NatGeo Wild Movies')  # probably will never happen
    # else:
    #     if affiliate_network == 'FX':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name)
    #     elif affiliate_network == 'FXX':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name)
    #     elif affiliate_network == 'FXM':
    #         pass  # should not have this if not is_movie
    #     elif affiliate_network == 'National Geographic':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name)
    #     elif affiliate_network == 'Nat Geo Wild':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name)

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_gci(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Movies/Free Movies")
        replacement_mapping.append("Free On Demand/ " + affiliate_network + "/Movies")
    else:
        replacement_mapping.append(
            "TV/Networks/" + affiliate_network + "/" + content_name + " HD"
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Movies/Free Movies")
        replacement_mapping.append("TV/Networks/" + affiliate_network + "/Movies")
    else:
        replacement_mapping.append(
            "TV/Networks/" + affiliate_network + "/" + content_name
        )
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_hbc(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    # affiliate_network = content_json.get('network', {}).get('value', '')
    #
    # content_name = content_json.get('content_name', {}).get('value', '')
    # if content_name.startswith('The '):
    #     content_name = content_name[4:]
    # if content_name.startswith('A '):
    #     content_name = content_name[2:]
    # if content_name.startswith('An '):
    #     content_name = content_name[3:]
    #
    # # content_first_letter = content_name.strip()[:1]
    # # showname_range = cox_get_alpha_range(content_first_letter)
    #
    # # #### HD Mappings ####
    # replacement_mapping = []
    # if is_movie:
    #     replacement_mapping.append('Movies/Free Movies')
    #     replacement_mapping.append('Free On Demand/ ' + affiliate_network +'/Movies')
    # else:
    #     replacement_mapping.append('TV/Networks/' + affiliate_network + '/' + content_name + ' HD')
    #
    # aff_data['category_mapping_list_hd'] = replacement_mapping
    #
    # # #### SD Mappings ####
    # replacement_mapping = []
    # if is_movie:
    #     replacement_mapping.append('Movies/Free Movies')
    #     replacement_mapping.append('TV/Networks/' + affiliate_network + '/Movies')
    # else:
    #     replacement_mapping.append('TV/Networks/' + affiliate_network + '/' + content_name)
    # aff_data['category_mapping_list'] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_hotwire(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    if affiliate_network == "FXM":
        affiliate_network = "FX Movie Channel"

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Movies/Free Movies/" + affiliate_network + " HD")
    else:
        replacement_mapping.append(
            "Networks on Demand/" + affiliate_network + "/" + content_name + " HD"
        )
        replacement_mapping.append(
            "Networks on Demand HD/" + affiliate_network + "/" + content_name + " HD"
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("Movies/Free Movies/" + affiliate_network)
    else:
        replacement_mapping.append(
            "Networks on Demand/" + affiliate_network + "/" + content_name
        )

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_mctv(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")  # no colon in names

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    replacement_mapping = []

    # generate network mapping
    replacement_mapping.append("TV Networks/" + affiliate_network + "/" + content_name)

    aff_data["category_mapping_list_hd"] = (
        replacement_mapping  # MCTV is SD only - so this won't be used. Just in case.
    )
    aff_data["category_mapping_list"] = replacement_mapping
    return aff_data


def affiliate_generate_mvpd_values_mediacom(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")  # no colon in names

    content_first_letter = content_name.strip()[:1]
    showname_range = mediacom_get_alpha_range(content_first_letter)

    replacement_mapping = []

    # generate network mapping
    replacement_mapping.append(
        "TV SHOWS A-Z/" + showname_range.upper() + "/" + content_name.upper()
    )
    replacement_mapping.append(
        "TV/TV NETWORKS/" + affiliate_network.upper() + "/" + content_name.upper()
    )

    aff_data["category_mapping_list_hd"] = replacement_mapping
    aff_data["category_mapping_list"] = replacement_mapping
    return aff_data


def affiliate_generate_mvpd_values_mobi(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    # if affiliate_network == 'FX':
    #     aff_data['provider'] = 'MP_FXHD'
    # elif affiliate_network == 'FXX':
    #     pass  # keep defaults
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['provider'] = 'NAT_GEOHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['provider'] = 'NAT_GEO_WILD_HD'
    #
    # # override Product Code for some networks
    # if affiliate_network == 'FX':
    #     aff_data['product_code'] = 'MPT'
    # elif affiliate_network == 'FXX':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['product_code'] = 'FZHD'
    #
    # # override PCT for some networks
    # if affiliate_network == 'FX':
    #     aff_data['pct'] = 'FX_PRIMETIME_FF_C3'
    # elif affiliate_network == 'FXX':
    #     pass # keep default
    # elif affiliate_network == 'FXM':
    #     pass  # keep default
    # elif affiliate_network == 'National Geographic':
    #     pass  # keep default
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['pct'] = 'NATIONALGEOGRAPHIC_EXPANDED_HD'

    # Cox category mappings - two types:
    # Category based on network:             TV/TV Networks/Networks D-G/FX/Welcome Wrexham HD
    # Category based on show name (series):  TV/TV Shows/V-Z/Welcome Wrexham HD
    # Category based on show name (movie):   Movies/Network Movies/FX Movies

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    replacement_mapping = []

    # generate network mapping
    if affiliate_network == "FX":
        replacement_mapping.append("TV Shows/By Network/FX/" + content_name + " HD")
    elif affiliate_network == "FXX":
        replacement_mapping.append("TV Shows/By Network/FXX/" + content_name + " HD")
    elif affiliate_network == "FXM":
        replacement_mapping.append("Movies/By Network/FXM/")  # untested
    elif affiliate_network == "National Geographic":
        replacement_mapping.append("Shows/" + content_name_clean + " HD")
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append("Shows/" + content_name_clean + " HD")  # untested

    # # generate show mapping
    # if is_movie:
    #     if affiliate_network == 'FX':
    #         replacement_mapping.append('Movies/Network Movies/FX Movies')
    #     elif affiliate_network == 'FXX':
    #         replacement_mapping.append('Movies/Network Movies/FXX Movies')  # not sure if this is correct
    #     elif affiliate_network == 'FXM':
    #         pass  # category already added at network level above - don't need a 2nd one for Cox
    #     elif affiliate_network == 'National Geographic':
    #         replacement_mapping.append('Movies/Network Movies/NatGeo Movies')  # probably will never happen
    #     elif affiliate_network == 'Nat Geo Wild':
    #         replacement_mapping.append('Movies/Network Movies/NatGeo Wild Movies')  # probably will never happen
    # else:
    #     if affiliate_network == 'FX':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name + ' HD')
    #     elif affiliate_network == 'FXX':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name + ' HD')
    #     elif affiliate_network == 'FXM':
    #         pass  # should not have this if not is_movie
    #     elif affiliate_network == 'National Geographic':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name + ' HD')
    #     elif affiliate_network == 'Nat Geo Wild':
    #         replacement_mapping.append('TV/TV Shows/' + showname_range + '/' + content_name + ' HD')

    aff_data["category_mapping_list"] = replacement_mapping
    return aff_data


def affiliate_generate_mvpd_values_sectv(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "SECV On Demand/Free On Demand A-Z/" + affiliate_network + " HD"
        )
    else:
        replacement_mapping.append(
            "SECV On Demand/Free On Demand A-Z/" + affiliate_network + " HD"
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "SECV On Demand/Free On Demand A-Z/" + affiliate_network
        )
    else:
        replacement_mapping.append(
            "SECV On Demand/Free On Demand A-Z/" + affiliate_network
        )

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_sectvlehigh(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "Service Electric VOD/Free OnDemand A-Z/"
            + affiliate_network
            + " HD/"
            + content_name
        )
    else:
        replacement_mapping.append(
            "Service Electric VOD/Free OnDemand A-Z/"
            + affiliate_network
            + " HD/"
            + content_name
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "Service Electric VOD/Free OnDemand A-Z/"
            + affiliate_network
            + "/"
            + content_name
        )
    else:
        replacement_mapping.append(
            "Service Electric VOD/Free OnDemand A-Z/"
            + affiliate_network
            + "/"
            + content_name
        )

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_sectvsparta(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "VODlink Root/Service Electric/FREE/Cable TV Favorites/"
            + affiliate_network
            + "/"
            + content_name
        )
    else:
        replacement_mapping.append(
            "VODlink Root/Service Electric/FREE/Cable TV Favorites/"
            + affiliate_network
            + "/"
            + content_name
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "VODlink Root/Service Electric/FREE/Cable TV Favorites/"
            + affiliate_network
            + "/"
            + content_name
        )
    else:
        replacement_mapping.append(
            "VODlink Root/Service Electric/FREE/Cable TV Favorites/"
            + affiliate_network
            + "/"
            + content_name
        )

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_tangerine_allo(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    season_num = content_json.get("season_num", {}).get("value", "")
    # if affiliate_network == 'FX':
    #     aff_data['provider'] = 'MP_FXHD'
    # elif affiliate_network == 'FXX':
    #     pass  # keep defaults
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['provider'] = 'NAT_GEOHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['provider'] = 'NAT_GEO_WILD_HD'
    #
    # # override Product Code for some networks
    # if affiliate_network == 'FX':
    #     aff_data['product_code'] = 'MPT'
    # elif affiliate_network == 'FXX':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['product_code'] = 'FZHD'
    #
    # # override PCT for some networks
    # if affiliate_network == 'FX':
    #     aff_data['pct'] = 'FX_PRIMETIME_FF_C3'
    # elif affiliate_network == 'FXX':
    #     pass # keep default
    # elif affiliate_network == 'FXM':
    #     pass  # keep default
    # elif affiliate_network == 'National Geographic':
    #     pass  # keep default
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['pct'] = 'NATIONALGEOGRAPHIC_EXPANDED_HD'

    # Cox category mappings - two types:
    # Category based on network:             TV/TV Networks/Networks D-G/FX/Welcome Wrexham HD
    # Category based on show name (series):  TV/TV Shows/V-Z/Welcome Wrexham HD
    # Category based on show name (movie):   Movies/Network Movies/FX Movies

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    replacement_mapping = []

    # generate network mapping
    if affiliate_network == "FX":
        replacement_mapping.append(
            "Hollywood/TV Shows/Entertainment/FX/" + content_name_clean + " HD"
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Channel/FX/" + content_name_clean + " HD"
        )
    elif affiliate_network == "FXX":
        replacement_mapping.append(
            "Hollywood/TV Shows/Entertainment/FXX/" + content_name_clean + " HD"
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Channel/FXX/" + content_name_clean + " HD"
        )
    elif affiliate_network == "FXM":
        replacement_mapping.append("Hollywood/Movies/Entertainment/FXM")  # untested
        replacement_mapping.append("Hollywood/Movies/By Channel/FXM")  # untested
    elif affiliate_network == "National Geographic":
        replacement_mapping.append(
            "Hollywood/TV Shows/By Category/Lifestyle/National Geographic/"
            + content_name_clean
            + " HD"
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Channel/National Geographic/"
            + content_name_clean
            + " HD"
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Category/Lifestyle/National Geographic/"
            + content_name_clean
            + " HD"
            + "/Season "
            + str(season_num)
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Channel/National Geographic/"
            + content_name_clean
            + " HD"
            + "/Season "
            + str(season_num)
        )
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append(
            "Hollywood/TV Shows/By Category/Lifestyle/NatGeo Wild/"
            + content_name_clean
            + " HD"
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Channel/NatGeo Wild/" + content_name_clean + " HD"
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Category/Lifestyle/NatGeo Wild/"
            + content_name_clean
            + " HD"
            + "/Season "
            + str(season_num)
        )
        replacement_mapping.append(
            "Hollywood/TV Shows/By Channel/NatGeo Wild/"
            + content_name_clean
            + " HD"
            + "/Season "
            + str(season_num)
        )

    aff_data["category_mapping_list"] = replacement_mapping
    return aff_data


def affiliate_generate_mvpd_values_tds_broadband(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    affiliate_network_hd = affiliate_network

    if affiliate_network == "National Geographic":
        affiliate_network_hd = "National Geo"

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    content_first_letter = content_name.strip()[:1]
    showname_range = tds_broadband_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "TV/TV Shows/" + showname_range + " HD/" + content_name
        )
        replacement_mapping.append(
            "TV/TV Networks/" + affiliate_network_hd + " HD/" + content_name
        )
    else:
        replacement_mapping.append(
            "TV/TV Shows/" + showname_range + " HD/" + content_name
        )
        replacement_mapping.append(
            "TV/TV Networks/" + affiliate_network_hd + " HD/" + content_name
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("TV/TV Shows/" + showname_range + "/" + content_name)
        replacement_mapping.append(
            "TV/TV Networks/" + affiliate_network + "/" + content_name
        )
    else:
        replacement_mapping.append("TV/TV Shows/" + showname_range + "/" + content_name)
        replacement_mapping.append(
            "TV/TV Networks/" + affiliate_network + "/" + content_name
        )
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_tds_telecom(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    affiliate_network_hd = affiliate_network

    if affiliate_network == "National Geographic":
        affiliate_network = "Nat Geo"

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    content_first_letter = content_name.strip()[:1]
    showname_range = tds_telecom_get_alpha_range(content_first_letter)

    # #### HD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append(
            "TV Shows/" + showname_range + "/" + content_name + " HD"
        )
        replacement_mapping.append(
            "TV Shows/TV Network/" + affiliate_network + "/" + content_name + " HD"
        )
    else:
        replacement_mapping.append(
            "TV Shows/" + showname_range + "/" + content_name + " HD"
        )
        replacement_mapping.append(
            "TV Shows/TV Network/" + affiliate_network + "/" + content_name + " HD"
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # #### SD Mappings ####
    replacement_mapping = []
    if is_movie:
        replacement_mapping.append("TV Shows/" + showname_range + "/" + content_name)
        replacement_mapping.append(
            "TV Shows/TV Network/" + affiliate_network + "/" + content_name
        )
    else:
        replacement_mapping.append("TV Shows/" + showname_range + "/" + content_name)
        replacement_mapping.append(
            "TV Shows/TV Network/" + affiliate_network + "/" + content_name
        )
    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_vubiquity_wow(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    season_num = content_json.get("season_num", {}).get("value", "")
    # if affiliate_network == 'FX':
    #     aff_data['provider'] = 'MP_FXHD'
    # elif affiliate_network == 'FXX':
    #     pass  # keep defaults
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['provider'] = 'NAT_GEOHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['provider'] = 'NAT_GEO_WILD_HD'
    #
    # # override Product Code for some networks
    # if affiliate_network == 'FX':
    #     aff_data['product_code'] = 'MPT'
    # elif affiliate_network == 'FXX':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['product_code'] = 'FZHD'
    #
    # # override PCT for some networks
    # if affiliate_network == 'FX':
    #     aff_data['pct'] = 'FX_PRIMETIME_FF_C3'
    # elif affiliate_network == 'FXX':
    #     pass # keep default
    # elif affiliate_network == 'FXM':
    #     pass  # keep default
    # elif affiliate_network == 'National Geographic':
    #     pass  # keep default
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['pct'] = 'NATIONALGEOGRAPHIC_EXPANDED_HD'

    # Cox category mappings - two types:
    # Category based on network:             TV/TV Networks/Networks D-G/FX/Welcome Wrexham HD
    # Category based on show name (series):  TV/TV Shows/V-Z/Welcome Wrexham HD
    # Category based on show name (movie):   Movies/Network Movies/FX Movies

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    # content_first_letter = content_name.strip()[:1]
    # showname_range = cox_get_alpha_range(content_first_letter)

    replacement_mapping = []

    # generate HD mapping
    if affiliate_network == "FX":
        replacement_mapping.append("HD On Demand/FX HD/" + content_name_clean + " HD")
    elif affiliate_network == "FXX":
        replacement_mapping.append(
            "HD On Demand/FXX HD/" + content_name_clean + " HD"
        )  # untested
    elif affiliate_network == "FXM":
        replacement_mapping.append("HD On Demand/FXM HD")  # untested
    elif affiliate_network == "National Geographic":
        replacement_mapping.append(
            "HD On Demand/NG HD/" + content_name_clean + " HD"
        )  # untested
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append(
            "HD On Demand/NGW HD/" + content_name_clean + " HD"
        )  # untested

    aff_data["category_mapping_list_hd"] = replacement_mapping
    return aff_data


def affiliate_generate_mvpd_values_verizon(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    # override Provider for some networks
    affiliate_network = content_json.get("network", {}).get("value", "")
    season_num = content_json.get("season_num", {}).get("value", "")
    # if affiliate_network == 'FX':
    #     aff_data['provider'] = 'MP_FXHD'
    # elif affiliate_network == 'FXX':
    #     pass  # keep defaults
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['provider'] = 'NAT_GEOHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['provider'] = 'NAT_GEO_WILD_HD'
    #
    # # override Product Code for some networks
    # if affiliate_network == 'FX':
    #     aff_data['product_code'] = 'MPT'
    # elif affiliate_network == 'FXX':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'FXM':
    #     pass  # keep defaults
    # elif affiliate_network == 'National Geographic':
    #     aff_data['product_code'] = 'FZHD'
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['product_code'] = 'FZHD'
    #
    # # override PCT for some networks
    # if affiliate_network == 'FX':
    #     aff_data['pct'] = 'FX_PRIMETIME_FF_C3'
    # elif affiliate_network == 'FXX':
    #     pass # keep default
    # elif affiliate_network == 'FXM':
    #     pass  # keep default
    # elif affiliate_network == 'National Geographic':
    #     pass  # keep default
    # elif affiliate_network == 'Nat Geo Wild':
    #     aff_data['pct'] = 'NATIONALGEOGRAPHIC_EXPANDED_HD'

    # Cox category mappings - two types:
    # Category based on network:             TV/TV Networks/Networks D-G/FX/Welcome Wrexham HD
    # Category based on show name (series):  TV/TV Shows/V-Z/Welcome Wrexham HD
    # Category based on show name (movie):   Movies/Network Movies/FX Movies

    content_name = content_json.get("content_name", {}).get("value", "")
    if content_name.startswith("The "):
        content_name = content_name[4:]
    if content_name.startswith("A "):
        content_name = content_name[2:]
    if content_name.startswith("An "):
        content_name = content_name[3:]

    content_name_clean = content_name.replace(":", "")

    content_first_letter = content_name.strip()[:1]
    showname_range = verizon_get_alpha_range(content_first_letter)

    # generate HD mapping
    replacement_mapping = []
    if affiliate_network == "FX":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/FX/FX HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/D - G/FX/FX HD/" + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/FX/FX HD/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/D - G/FX/FX HD/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )
    elif affiliate_network == "FXX":  # untested
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/FXX/FXX HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/D - G/FXX/FXX HD/" + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/FXX/FXX HD/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/D - G/FXX/FXX HD/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )
    elif affiliate_network == "FXM":  # untested
        replacement_mapping.append("Movies/By Genre/Entertainment/FXM/FXM HD")
        replacement_mapping.append("Movies/By Network/D - G/FXM/FXM HD")
        replacement_mapping.append("Movies/By Title/" + showname_range)
        # replacement_mapping.append('TV Shows/By Genre/Entertainment/FX/FX HD/' + content_name_clean)
        # replacement_mapping.append('TV Shows/By Network/D - G/FX/FX HD/' + content_name_clean)
        # replacement_mapping.append('TV Shows/By Show/' + showname_range + '/' + content_name_clean)
    elif affiliate_network == "National Geographic":  # untested
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/N - R/Nat Geo/Nat Geo HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/N - R/Nat Geo/Nat Geo HD/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Science & Nature/Nat Geo Wild/Nat Geo Wild HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Science & Nature/Nat Geo Wild/Nat Geo Wild HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild HD/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )

    aff_data["category_mapping_list_hd"] = replacement_mapping

    # generate SD mapping
    replacement_mapping = []
    if affiliate_network == "FX":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/FX/FX/" + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/D - G/FX/FX/" + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/FX/FX/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/D - G/FX/FX/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )
    elif affiliate_network == "FXX":  # untested
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Entertainment/FXX/FXX/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/D - G/FXX/FXX/" + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Entertainment/FXX/FXX/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/D - G/FXX/FXX/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )
    elif affiliate_network == "FXM":  # untested
        replacement_mapping.append("Movies/By Genre/Entertainment/FXM/FXM")
        replacement_mapping.append("Movies/By Network/D - G/FXM/FXM")
        replacement_mapping.append("Movies/By Title/" + showname_range)
        # replacement_mapping.append('TV Shows/By Genre/Entertainment/FX/FX HD/' + content_name_clean)
        # replacement_mapping.append('TV Shows/By Network/D - G/FX/FX HD/' + content_name_clean)
        # replacement_mapping.append('TV Shows/By Show/' + showname_range + '/' + content_name_clean)
    elif affiliate_network == "National Geographic":  # untested
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/N - R/Nat Geo/Nat Geo/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Science & Nature/Nat Geo/Nat Geo/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/N - R/Nat Geo/Nat Geo/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )
    elif affiliate_network == "Nat Geo Wild":
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Genre/Science & Nature/Nat Geo Wild/Nat Geo Wild/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild/"
            + content_name_clean
        )
        replacement_mapping.append(
            "Free & Premium/TV Shows/By Show/"
            + showname_range
            + "/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Genre/Science & Nature/Nat Geo Wild/Nat Geo Wild/"
            + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Network/N - R/Nat Geo Wild/Nat Geo Wild/" + content_name_clean
        )
        replacement_mapping.append(
            "TV Shows/By Show/" + showname_range + "/" + content_name_clean
        )

    aff_data["category_mapping_list"] = replacement_mapping

    return aff_data


def affiliate_generate_mvpd_values_vermont(content_json, is_movie):
    """
    Extract and map data for affiliate's expected data
    :param content_json:
    :return:
    """
    # they get defaults until proven otherwise
    aff_data = affiliate_generate_mvpd_default_values(content_json)

    return aff_data


def armstrong_get_alpha_range(letter):
    if len(letter) > 1:
        letter = letter[0]

    try:
        st_index = string.ascii_lowercase.index(letter.lower())
    except:
        # this is for series that start with numbers like "9-1-1"
        st_index = 0

    if st_index <= 5:
        return "A - F"
    elif st_index >= 6 and st_index <= 12:
        return "G - M"
    elif st_index >= 13 and st_index <= 18:
        return "N - S"
    elif st_index >= 19:
        return "T - Z"


def cox_get_alpha_range(letter):
    if len(letter) > 1:
        letter = letter[0]

    try:
        st_index = string.ascii_lowercase.index(letter.lower())
    except:
        st_index = 0

    if st_index <= 2:
        return "A-C"
    elif st_index >= 3 and st_index <= 5:
        return "D-F"
    elif st_index >= 6 and st_index <= 8:
        return "G-I"
    elif st_index >= 9 and st_index <= 11:
        return "J-L"
    elif st_index >= 12 and st_index <= 14:
        return "M-O"
    elif st_index >= 15 and st_index <= 17:
        return "P-R"
    elif st_index >= 18 and st_index <= 20:
        return "S-U"
    elif st_index >= 21:
        return "V-Z"


def mediacom_get_alpha_range(letter):
    if len(letter) > 1:
        letter = letter[0]

    try:
        st_index = string.ascii_lowercase.index(letter.lower())
    except:
        st_index = 0

    if st_index <= 2:
        return "A-C"  # untested
    elif st_index >= 3 and st_index <= 6:
        return "D-G"  # untested
    elif st_index >= 7 and st_index <= 9:
        return "H-J"  # untested
    elif st_index >= 10 and st_index <= 12:
        return "K-M"  # untested
    elif st_index >= 13 and st_index <= 17:
        return "N-R"  # untested
    elif st_index >= 18 and st_index <= 19:
        return "S-T"  # untested
    elif st_index >= 20 and st_index <= 21:
        return "U-V"  # untested
    elif st_index >= 22:
        return "W-Z"


def tds_broadband_get_alpha_range(letter):
    if len(letter) > 1:
        letter = letter[0]

    try:
        st_index = string.ascii_lowercase.index(letter.lower())
    except:
        st_index = 0

    if st_index <= 2:
        return "A-C"
    elif st_index >= 3 and st_index <= 5:
        return "D-F"
    elif st_index >= 6 and st_index <= 8:
        return "G-I"
    elif st_index >= 9 and st_index <= 11:
        return "J-L"
    elif st_index >= 12 and st_index <= 15:
        return "M-P"
    elif st_index >= 16 and st_index <= 18:
        return "Q-S"
    elif st_index >= 19:
        return "T-Z"


def tds_telecom_get_alpha_range(letter):
    if len(letter) > 1:
        letter = letter[0]

    # if first character is a number, return the 0-9 range
    num_string = "0123456789"
    # for this_num in range(10):
    #     num_string = num_string + str(this_num)
    if str(letter) in num_string:  # put it in a str() just in case
        return "0-9"

    # otherwise, let's see where this alpha character is located in alphabet
    st_index = string.ascii_lowercase.index(letter.lower())

    if st_index == 0:
        return "A"
    elif st_index == 1:
        return "B"
    elif st_index == 2:
        return "C"
    elif st_index == 3:
        return "D"
    elif st_index >= 4 and st_index <= 5:
        return "E-F"
    elif st_index >= 6 and st_index <= 7:
        return "G-H"
    elif st_index >= 8 and st_index <= 10:
        return "I-K"
    elif st_index == 11:
        return "L"
    elif st_index == 12:
        return "M"
    elif st_index >= 13 and st_index <= 14:
        return "N-O"
    elif st_index == 15:
        return "P"
    elif st_index >= 16 and st_index <= 17:
        return "Q-R"
    elif st_index == 18:
        return "S"
    elif st_index == 19:
        return "T"
    elif st_index >= 20:
        return "U-Z"


def verizon_get_alpha_range(letter):
    if len(letter) > 1:
        letter = letter[0]

    try:
        st_index = string.ascii_lowercase.index(letter.lower())
    except:
        st_index = 0

    if st_index <= 2:
        return "A - C"  # untested
    elif st_index >= 3 and st_index <= 6:
        return "D - G"
    elif st_index >= 7 and st_index <= 9:
        return "H - J"  # untested
    elif st_index >= 10 and st_index <= 12:
        return "K - M"  # untested
    elif st_index >= 13 and st_index <= 17:
        return "N - R"
    elif st_index >= 18 and st_index <= 19:
        return "S - T"
    elif st_index >= 20 and st_index <= 21:
        return "U - V"  # untested
    elif st_index >= 22:
        return "W - Z"  # untested


def titleapi_get_authz_token(client_id, client_secret):
    url = "https://cp-auth-service.maestrosb.dmed.technology/v2/as/token.oauth2"
    params = {
        "client_id": client_id,
        "client_secret": client_secret,
        "grant_type": "client_credentials",
        "scope": "dtci-theforce-title-general-only",
    }

    resp = requests.post(url, params=params)
    if resp.status_code == requests.codes.ok:
        # print resp.cookies

        # print(('Login to Reach successful at {}').format(fqdn))
        # return resp.cookies  # Reach 7
        try:
            return {"Authorization": "Bearer " + resp.json()["access_token"]}
        except KeyError as e:
            raise Exception(
                "Unable to authenticate with AuthZ for TitleAPI - unexpected result. Error: {}".format(
                    e
                )
            )
    else:
        raise Exception("Unable to authenticate with AuthZ for TitleAPI")


def titleapi_search_for_title(title, titleapi_token):
    url = "https://title-api.maestrosb.dmed.technology/graphql"
    graphql_query = {
        "query": """query {
    searchTitles(input: {stringFilterFields: [ { field: NAME, values: [ \""""
        + title
        + """\"] } ]}) {
    totalCount,
    edges {
      node {
        name
        episodeNumber
        series {
          id
        }
        season {
          id
        }
        seasons {
          edges {
            node {
              id
            }
          }
        }
        productId
        producedBy
        productGroupId
        keywords
        eidr
        productionNumber
        productClass
        productType
        externalIds {
          comscoreTitleId
        }
        episodes {
          edges {
            node {
              id
            }
          }
        }
      }
    }
  }
}"""
    }

    response = requests.post(url=url, headers=titleapi_token, json=graphql_query)
    if response.status_code == requests.codes.ok:
        return response.json()
    else:
        raise Exception(
            "TitleAPI query failed. HTTP Error: {} - {}".format(
                response.status_code, response.reason
            )
        )


def get_comcast_preroll_name_given_network(network):
    if network.lower().strip() == "disney xd":
        return "DXD JEPB1215 - PREROLL with 05 Black"

    if network.lower().strip() == "disney channel":
        return "DC Preroll DEPP2963 with 05 BLACK"

    if network.lower().strip() == "disney jr":
        return "DJR Preroll REPP1299 with 05 BLACK"

    # default is 5 seconds black
    return "5 Seconds Black"


def cleanup_distribution_categories_and_partners(content_json):
    """
    Designed to help clean up the shortcut phrases used by Distro team for selecting partners and categories
    Example: when they say partner "DATG", they really mean "DTCI Video Platforms"
    Example 2 : when they say "Full Episode category" they mean "Episode"
    :param category_list_dirty:
    :return:
    """
    all_reach_categories = [
        "Episode",
        "Episode Bonus",
        "Episode C Type",
        "Episode D Type",
        "Movie C Type",
        "Movie D Type",
        "Movie (Made For TV)",
        "Movie (Theatrical Formatted for TV)",
        "On-Air Promo",
        "Special",
        "Special C Type",
        "Special D Type",
        "Storybook",
        "Behind The Scenes",
        "Episode Short",
        "Music Video",
        "Show Clip",
        "Trailer",
        "Webisode",
    ]

    common_partners = [
        "ANET",
        "Charter (via Deluxe)",
        "Comcast Unified",
        "DirecTV",
        "DISH",
        "DTCI Video Platforms",
        "FuboTV",
        "Genome",
        "Hulu",
        "Hulu dMVPD",
        "Verizon TVE",
        # 'Vidgo',
        "YouTube dMVPD",
    ]

    # cleanup human text to exactly what Reach expects
    dirty_category_list = content_json["categories_and_distribution"]["value"]

    for dirty_cat_counter in range(len(dirty_category_list)):
        # for this_dirty_cat in dirty_category_list:
        this_dirty_cat = dirty_category_list[dirty_cat_counter]
        dirty_cat_type = this_dirty_cat.get("type")  # a string of the category name
        dirty_cat_partners = this_dirty_cat.get(
            "partners"
        )  # this is a [list] of all partners for this category
        # print('dirty_cat_type = {}'.format(dirty_cat_type))
        # Cleanup category _______________
        if "full episode" in dirty_cat_type.lower():
            dirty_cat_type = "Episode"

        if "behind-the-scenes" in dirty_cat_type.lower():
            dirty_cat_type = "Behind The Scenes"

        # get rid of any "category" words
        if dirty_cat_type.lower().strip().endswith("category"):
            dirty_cat_type = dirty_cat_type.replace("Category", "")
            dirty_cat_type = dirty_cat_type.replace("category", "")
            dirty_cat_type = dirty_cat_type.strip()

        # print('dirty_cat_type = {}'.format(dirty_cat_type))
        # loop through known categories, try to find closest match
        exact_match_found = False
        for this_known_category in all_reach_categories:
            match_ratio = fuzz.ratio(
                dirty_cat_type.lower(), this_known_category.lower()
            )
            if match_ratio == 100:
                dirty_cat_type = this_known_category
                exact_match_found = True
                break

        if not exact_match_found:
            for this_known_category in all_reach_categories:
                match_ratio = fuzz.ratio(
                    dirty_cat_type.lower(), this_known_category.lower()
                )
                if match_ratio > 95:
                    dirty_cat_type = this_known_category
                    break

        this_dirty_cat["type"] = dirty_cat_type

        # Cleanup partners _______________

        for part_count in range(len(dirty_cat_partners)):
            # for this_dirty_partner in dirty_cat_partners:
            this_dirty_partner = dirty_cat_partners[part_count]
            # print('this_dirty_partner={}'.format(this_dirty_partner))
            if this_dirty_partner.lower() == "datg":
                this_dirty_partner = "DTCI Video Platforms"

            if this_dirty_partner.lower() == "dtci":
                this_dirty_partner = "DTCI Video Platforms"

            if this_dirty_partner.lower() == "hulu svod":
                this_dirty_partner = "Hulu"

            if this_dirty_partner.lower() == "charter":
                this_dirty_partner = "Charter (via Deluxe)"

            exact_partner_match = False
            for this_known_partner in common_partners:
                match_ratio = fuzz.ratio(
                    this_dirty_partner.lower(), this_known_partner.lower()
                )
                # print('Match {}%: {} == {}'.format(match_ratio, this_dirty_partner, this_known_partner))
                if match_ratio == 100:
                    this_dirty_partner = this_known_partner
                    exact_partner_match = True
                    break

            if not exact_partner_match:
                for this_known_partner in common_partners:
                    match_ratio = fuzz.ratio(
                        this_dirty_partner.lower(), this_known_partner.lower()
                    )
                    # print('Match {}%: {} == {}'.format(match_ratio, this_dirty_partner, this_known_partner))
                    if match_ratio >= 90:
                        this_dirty_partner = this_known_partner
                        break

            dirty_cat_partners[part_count] = this_dirty_partner

        # update the data struct
        this_dirty_cat["partners"] = dirty_cat_partners
        dirty_category_list[dirty_cat_counter] = this_dirty_cat
        content_json["categories_and_distribution"]["value"] = dirty_category_list
        # return category_list_dirty if this isn't correctly implemented
    return content_json


def get_default_profiles_given_network(content_json):
    network = content_json.get("network", {}).get("value", "")

    network_var = ""
    if "fx" in network.lower():  # FX, FXX, FXM
        network_var = "fx"
    elif (
        "nat geo" in network.lower()
        or "natgeo" in network.lower()
        or "ngc" in network.lower()
    ):
        network_var = "fx"
    elif "national" in network.lower() and "geographic" in network.lower():
        network_var = "fx"
    elif "abc localish studios" in network.lower():
        network_var = "localish"
    else:
        network_var = "abc"

    # ADD MEDIA PROFILES  ##############################
    fx_default_profiles = [
        {
            "type": "Long Form Content",
            "profile": "PNG 1280x720",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "PNG 1920x1080",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1280x720 59.94p 8-24 Track - 5.1/2.0 - 01HR TC ",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 5.1/2.0 - 01HR TC ",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 29.97p 8-24 Track - 5.1/2.0 - 01HR TC ",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MXF 1280x720 59.94p 50mbps PCM 8Ch w/CC 01hrTC",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MPEG-TS 1280x720 59.94p 50mbps AC3 6Ch w/CC 00hrTC (D-Type from MH)",
            "validation": "REQUIRED",
        },
        {"type": "Long Form Content", "profile": "WAV 2.0", "validation": "OPTIONAL"},
    ]

    abc_default_long_profiles = [
        {
            "type": "Long Form Content",
            "profile": "PNG 1280x720",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1280x720 59.94p 8-24 Track - 5.1/2.0 - 01HR TC ",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1280x720 59.94p 8-24 Track - 2.0 on Ch 7/8 - 01HR TC ",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 5.1/2.0 - 01HR TC ",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MASTER ProResHQ 1920x1080 23.98p 8-24 Track - 2.0 on Ch 7/8 - 01HR TC ",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "MXF 1280x720 59.94p 50mbps PCM 8Ch w/CC 01hrTC",
            "validation": "REQUIRED",
        },
        {"type": "Long Form Content", "profile": "WAV 2.0", "validation": "OPTIONAL"},
    ]

    abc_localish_default_long_profiles = [
        {
            "type": "Long Form Content",
            "profile": "JPG 1280x720",
            "validation": "REQUIRED",
        },
        {
            "type": "Long Form Content",
            "profile": "ProResHQ 1280x720 59.94p 2.0 (ABC Localish Studios ONLY)",
            "validation": "REQUIRED",
        },
    ]

    abc_default_short_profiles = [
        {
            "type": "Short Form Content",
            "profile": "PNG 1280x720",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1280x720 59.94p 5.1/2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1280x720 59.94p 2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1920x1080 23.98p 5.1/2.0",
            "validation": "REQUIRED",
        },
        {
            "type": "Short Form Content",
            "profile": "ProResHQ 1920x1080 23.98p 2.0",
            "validation": "REQUIRED",
        },
    ]

    if network_var == "fx":
        return fx_default_profiles

    if network_var == "localish":
        return abc_localish_default_long_profiles

    # catchall
    return abc_default_long_profiles + abc_default_short_profiles


def main(
    content_json, artwork_path, small_artwork_path, reach_token, reach_address, env
):
    notification_alerts = []
    # determine if series_json is for series or special:
    # look in categories_and_distribution - see if anything has "Special" or "Episode"
    # special = special,  episode = series
    if (
        content_json["program_type"]["value"] == "movie"
        or content_json["program_type"]["value"] == "special"
    ):
        is_movie = True
    else:
        is_movie = False

    # first, let's cleanup the json. Probably some typos or bad formatting from pesky humans
    content_json = cleanup_distribution_categories_and_partners(content_json)

    # SERIES/MOVIE CREATION ##############################
    # check Reach - see if series/special/movie already exists
    if (
        content_json["program_type"]["value"] == "movie"
        or content_json["program_type"]["value"] == "special"
    ):
        content_type = "Movie/Special"
    elif content_json["program_type"]["value"] == "series":
        content_type = "Series"
    elif content_json["program_type"]["value"] == "acquired":
        content_type = "Series"
    else:
        logger_service.error("program_type is missing in JSON")
        raise Exception("Missing data in JSON: program_type")

    if content_json.get("network", {}).get("value") is None:
        logger_service.error("network is missing in JSON")
        raise Exception("Missing data in JSON: network")

    network_name = content_json.get("network", {}).get("value")

    episode_c_type_str = content_json.get("episode_c_type", {}).get("value", "")
    c_win_length = 0
    if len(episode_c_type_str) > 0:
        try:
            c_win_length = int(episode_c_type_str[-1])
        except:
            pass

    if episode_c_type_str == "":
        if (
            "fx" in network_name.lower()
            or "national geographic" in network_name.lower()
            or "nat geo wild" in network_name.lower()
        ):
            c_win_length = 3
        else:
            c_win_length = 7

    # Update Jan 2025 - National Geographic and NatGeo Wild no longer have C3
    if (
        "national geographic" in network_name.lower()
        or "nat geo wild" in network_name.lower()
    ):
        c_win_length = 0

    # if content_json.get('network', {}).get('value') is None:
    #     logger_service.error('program_type is missing in JSON')
    #     raise Exception('Missing data in JSON: network')
    #
    #     content_type = 'Movie/Special'
    # elif content_json['program_type']['value'] == 'series':
    #     content_type = 'Series'
    # else:
    #     logger_service.error('program_type is missing in JSON')
    #     raise Exception('Missing data in JSON: program_type')

    logger_service.info(
        'Trying to find matching {} in Reach for "{}"'.format(
            content_type, content_json["content_name"]["value"]
        )
    )
    potential_content_matches = reach_search_for_series_or_special_or_movie(
        content_json["content_name"]["value"],
        content_type,
        content_json["network"]["value"],
        reach_token,
        reach_address,
    )

    # more than 1 match - throw an exception
    if len(potential_content_matches) > 1:
        notification_alerts.append(
            "Unable to determine if {} already exists in Reach.".format(content_type)
        )
        logger_service.error(
            "Unable to determine if {} already exists in Reach. Found items:".format(
                content_type
            )
        )
        for this_match in potential_content_matches:
            logger_service.error(this_match["name"])
        raise Exception(
            "Unable to determine if {} already in Reach".format(content_type)
        )

    if len(potential_content_matches) == 1:
        reach_content = potential_content_matches[0]
        logger_service.info(
            "Found one match: {}  (id {})".format(
                reach_content["name"], reach_content["id"]
            )
        )
        must_add_profiles = False

    # none found - create it
    if len(potential_content_matches) == 0:
        logger_service.info("No matches found - creating content in Reach")
        reach_content = reach_create_content(
            content_json, content_type, reach_token, reach_address
        )
        logger_service.info("New content id = {}".format(reach_content.get("id", "")))
        must_add_profiles = True
        # if is_movie:  # artwork at the content level (not season)
        #     logger_service.info('Creating artwork container for movie/special content id {}'.format(reach_content.get('id')))
        #     new_art_id = reach_create_content_artwork_container_given_content_id(reach_content.get('id'),
        #                                                                          reach_token,
        #                                                                          reach_address)

    reach_content_id = reach_content.get("id")
    logger_service.info(
        "Working with Reach content id = {} ({})".format(
            reach_content_id, reach_content.get("name")
        )
    )
    # check if we need to add artwork container to movie content
    new_art_id = reach_get_content_artwork_container_given_content_id(
        reach_content_id, reach_token, reach_address
    )
    if new_art_id == "":
        # need to make art container
        logger_service.info(
            "Creating artwork container for movie/special content id {}".format(
                reach_content_id
            )
        )
        new_art_id = reach_create_artwork_container_given_content_id(
            reach_content_id, reach_token, reach_address
        )
    else:
        logger_service.info(
            "Artwork container already exists (id {}). Skipping.".format(new_art_id)
        )

    if is_movie:
        all_artwork_already_added = reach_get_artwork_instances_given_content_id_movies(
            reach_content_id, reach_token, reach_address
        )
        large_artwork = all_artwork_already_added.get("large", "")
        small_artwork = all_artwork_already_added.get("small", "")
        # were we asked to add Large artwork for movie?
        if artwork_path != "":
            if large_artwork != "":
                logger_service.info("Movie already has large artwork. Skipping")
            if large_artwork == "":
                large_artwork = reach_add_artwork_file_to_artwork_container(
                    reach_content.get("id"),
                    "",
                    new_art_id,
                    artwork_path,
                    "large_poster",
                    reach_token,
                    reach_address,
                )
        # were we asked to add small artwork for movie?
        if small_artwork_path != "":
            if small_artwork != "":
                logger_service.info("Movie already has small artwork. Skipping")
            if small_artwork == "":
                small_artwork = reach_add_artwork_file_to_artwork_container(
                    reach_content.get("id"),
                    "",
                    new_art_id,
                    small_artwork_path,
                    "small_poster",
                    reach_token,
                    reach_address,
                )

        if small_artwork == "" or large_artwork == "":
            # new_art_url = '{0}/reachengine/api/abc/artwork/{1}'.format(reach_fqdn, new_art_id)
            new_art_url_webUI = "{0}/meta/movies/edit/{1}/artwork/edit/{2}".format(
                reach_address, reach_content.get("id", ""), new_art_id
            )
            notification_alerts.append(
                "Need to add artwork at {0}".format(new_art_url_webUI)
            )
        must_add_profiles = True

    default_profiles = get_default_profiles_given_network(content_json)

    if must_add_profiles:
        logger_service.info(
            "Adding media profiles to content id {}".format(reach_content_id)
        )
        reach_add_media_profiles_given_content_id(
            reach_content_id, default_profiles, reach_token, reach_address
        )
    else:
        logger_service.info(
            "Series/Movie was already created. Adding any missing media profiles."
        )
        reach_update_media_profiles_given_content_id(
            reach_content_id, default_profiles, reach_token, reach_address
        )

    #
    # COLLABORATORS (ACTORS)  ##############################
    # Add them to Reach, then add them to the Series
    content_json_actor_list = content_json["actors"]["value"]
    if len(content_json_actor_list) == 0:
        content_json_actor_list = [{"first": "NONE", "last": "", "character": "NONE"}]

    for this_actor in content_json_actor_list:
        this_actor_full = this_actor["first"] + " " + this_actor["last"]
        character_name = this_actor.get("character")
        actor_in_reach = reach_search_for_collaborators_in_reach(
            this_actor_full, reach_token, reach_address
        )
        if len(actor_in_reach) == 0:
            # need to add actor
            logger_service.info(
                'Adding collaborator "{}" to Reach'.format(
                    this_actor["first"] + " " + this_actor["last"]
                )
            )
            actor_in_reach = reach_create_collaborator(
                this_actor["first"], this_actor["last"], "", reach_token, reach_address
            )

        if len(actor_in_reach) == 1:
            actor_in_reach = actor_in_reach[0]
            logger_service.info(
                'Collaborator "{}" already in Reach.'.format(
                    this_actor["first"] + " " + this_actor["last"]
                )
            )

        # is actor added to the Series?
        content_collaborators = reach_get_collaborators_in_content(
            reach_content_id, reach_token, reach_address
        )
        actor_has_been_added_to_content = False
        for this_content_collaborator in content_collaborators:
            this_collab_name = this_content_collaborator.get("collaborator", {}).get(
                "displayName"
            )
            this_collab_char = this_content_collaborator.get("character", "")
            # compare both display name and the character name. (Animated series use multiple "NONE" for Display name)
            # add strip because some actor names might be one word (like NONE)
            if (this_collab_name.strip() == this_actor_full.strip()) and (
                this_collab_char.strip() == character_name.strip()
            ):
                logger_service.info(
                    'Collaborator "{}" already added to content id {}'.format(
                        this_actor_full, reach_content_id
                    )
                )
                actor_has_been_added_to_content = True

        if actor_has_been_added_to_content is False:
            logger_service.info(
                'Adding collaborator "{}" to content id {}'.format(
                    actor_in_reach.get("displayName"), reach_content_id
                )
            )
            reach_add_collaborator_given_content_id(
                actor_in_reach,
                character_name,
                reach_content_id,
                reach_token,
                reach_address,
            )

    #
    # CATEGORIES (Series or Special/Movie)  ##############################
    logger_service.info(" > > > CATEGORIES < < <")
    content_current_categories = reach_get_categories_given_content_id(
        reach_content_id, reach_token, reach_address
    )
    categories_from_json = content_json.get("categories_and_distribution", {}).get(
        "value", []
    )

    for this_json_category in categories_from_json:
        this_json_cat_name = this_json_category.get("type", "")
        if this_json_cat_name == "":
            continue

        requested_category_exists = False

        for this_existing_cat in content_current_categories:
            this_existing_cat_category_name = this_existing_cat.get("category", {}).get(
                "name"
            )
            if (
                this_existing_cat_category_name.strip().lower()
                == this_json_cat_name.strip().lower()
            ):
                # the requested category already exists - skip it
                requested_category_exists = True
                logger_service.info(
                    ' Category "{}" already exists in content id {}'.format(
                        this_json_cat_name, reach_content_id
                    )
                )
                break

        if not requested_category_exists:
            logger_service.info(
                ' Adding category "{}" to content id {}'.format(
                    this_json_cat_name, reach_content_id
                )
            )
            reach_add_category_given_content_id(
                reach_content_id, this_json_cat_name, reach_token, reach_address
            )

    # TODO remove this old commented code below
    # if is_movie is False:
    #     # compare to existing categories in content in Reach - if categories are not present, add them
    #
    #     # categories_from_json = content_json.get('categories_and_distribution', {}).get('value', [])
    #     # go through each category from json. see if already in Reach. If not, add it.
    #     for this_json_category in categories_from_json:
    #         this_json_cat_name = this_json_category.get('type', '')
    #         if this_json_cat_name == '':
    #             continue
    #
    #         requested_category_exists = False
    #     #has_episode = False
    #     #has_c_type = False
    #         for this_existing_cat in content_current_categories:
    #             this_existing_cat_category_name = this_existing_cat.get('category', {}).get('name')
    #             if this_existing_cat_category_name.strip().lower() == this_json_cat_name.strip().lower():
    #                 # the requested category already exists - skip it
    #                 requested_category_exists = True
    #                 break
    #
    #         if not requested_category_exists:
    #             logger_service.info('Adding category "{}" to content id {}'.format(this_json_cat_name, reach_content_id))
    #             reach_add_category_given_content_id(reach_content_id, this_json_cat_name, reach_token, reach_address)
    #
    #     # if has_episode is False:
    #     #     logger_service.info('Adding category "Episode" to content id {}'.format(reach_content_id))
    #     #     reach_add_category_given_content_id(reach_content_id, 'Episode', reach_token, reach_address)
    #     #
    #     # if has_c_type is False:
    #     #     logger_service.info('Adding category "Episode C Type" to content id {}'.format(reach_content_id))
    #     #     reach_add_category_given_content_id(reach_content_id, 'Episode C Type', reach_token, reach_address)
    #
    # if is_movie:
    #     # make sure "Movie (Theatrical Formatted for TV)" is in Series
    #     content_current_categories = reach_get_categories_given_content_id(reach_content_id, reach_token, reach_address)
    #     has_movie_category = False
    #     for this_cat in content_current_categories:
    #         this_cat_category_name = this_cat.get('category', {}).get('name')
    #         if this_cat_category_name == 'Movie (Theatrical Formatted for TV)':
    #             has_movie_category = True
    #
    #     if has_movie_category is False:
    #         logger_service.info('Adding category "Movie (Theatrical Formatted for TV)" to content id {}'.format(reach_content_id))
    #         reach_add_category_given_content_id(reach_content_id, 'Movie (Theatrical Formatted for TV)', reach_token, reach_address)

    #
    # SEASON CREATION  ##############################
    if is_movie is False:  # only add season for series, not movies
        new_season_needed = content_json["season_num"]["value"]
        logger_service.info(" > > > SEASON {} < < <".format(new_season_needed))
        existing_series_seasons = reach_get_seasons_given_series_id(
            reach_content_id, reach_token, reach_address
        )

        need_to_add_season = True
        for this_season in existing_series_seasons:
            this_season_num = this_season.get("value")
            if this_season_num is not None and (
                str(this_season_num) == str(new_season_needed)
            ):
                # the season we want already exists - no need to add it
                need_to_add_season = False
                season_json = this_season
                season_id = season_json["id"]
                break

        if need_to_add_season:
            small_artwork = ""
            large_artwork = ""
            logger_service.info(
                "Adding season {0} to content id {1}".format(
                    new_season_needed, reach_content_id
                )
            )
            season_json = reach_add_season_given_content_id(
                reach_content_id, new_season_needed, reach_token, reach_address
            )
            season_id = season_json["id"]

        else:
            pass
            logger_service.info(
                "Season {} already exists. Skipping.".format(new_season_needed)
            )

        # check if we need to add artwork to content (will be used as container for season art if necessary)
        new_art_id = reach_get_content_artwork_container_given_content_id(
            reach_content_id, reach_token, reach_address
        )
        if new_art_id == "":
            # need to make art container
            logger_service.info(
                "Creating Season artwork container for content id {}".format(
                    season_id, reach_content_id
                )
            )
            new_art_id = reach_create_artwork_container_given_content_id(
                reach_content_id, reach_token, reach_address
            )

        all_artwork_already_added = (
            reach_get_artwork_instances_given_content_id_and_season_id(
                reach_content_id, season_id, reach_token, reach_address
            )
        )
        large_artwork = all_artwork_already_added.get("large", "")
        small_artwork = all_artwork_already_added.get("small", "")

        # were we asked to add Large artwork?
        if artwork_path != "":
            if large_artwork != "":
                logger_service.info("Season already has large artwork. Skipping")
            if large_artwork == "":
                logger_service.info("Adding large artwork to season")
                large_artwork = reach_add_artwork_file_to_artwork_container(
                    reach_content_id,
                    season_id,
                    new_art_id,
                    artwork_path,
                    "large_poster",
                    reach_token,
                    reach_address,
                )

        # were we asked to add small artwork?
        if small_artwork_path != "":
            if small_artwork != "":
                logger_service.info("Season already has small artwork. Skipping")
            if small_artwork == "":
                logger_service.info("Adding small artwork to season")
                small_artwork = reach_add_artwork_file_to_artwork_container(
                    reach_content_id,
                    season_id,
                    new_art_id,
                    small_artwork_path,
                    "small_poster",
                    reach_token,
                    reach_address,
                )

        # check if all artwork has been populated. Alert user if missing
        if small_artwork == "" or large_artwork == "":
            # new_art_url = '{0}/reachengine/api/abc/artwork/{1}'.format(reach_fqdn, new_art_id)
            new_art_url_webUI = (
                "{0}/meta/series/edit/{1}/seasons/{2}/artwork/edit/{3}".format(
                    reach_address, reach_content_id, season_id, new_art_id
                )
            )

            notification_alerts.append(
                "Need to add season {0} artwork at {1}".format(
                    new_season_needed, new_art_url_webUI
                )
            )

            # TODO check artwork - see if there is artwork at season level. if not, add notification alert

        # add collaborators to Season
        logger_service.info(" > > > COLLABORATORS (SEASON) < < <")
        content_json_actor_list = content_json["actors"]["value"]
        if len(content_json_actor_list) == 0:
            content_json_actor_list = [
                {"first": "NONE", "last": "", "character": "NONE"}
            ]
        for this_actor in content_json_actor_list:
            this_actor_full = this_actor["first"] + " " + this_actor["last"]
            this_actor_char_name = this_actor["character"]
            # logger_service.info('Should add collaborator "{}" to season id {} of content id {} ?'.format(this_actor_full, season_id, reach_content_id))

            # do this twice because of Reach bug:
            for _ in range(2):
                reach_add_collaborator_given_content_id_to_season_id(
                    this_actor_full,
                    this_actor_char_name,
                    reach_content_id,
                    season_id,
                    reach_token,
                    reach_address,
                )
                if _ == 0:
                    # logger_service.info('Going to add collaborators to season again because of Reach bug.')
                    logger_service.info(
                        "Verifying collaborator in season ({} - {})".format(
                            this_actor_full, this_actor_char_name
                        )
                    )
                sleep(2)

    # subscriptions
    logger_service.info(" > > > SUBSCRIPTIONS < < <")
    # subscribe to partners
    categories_from_json = content_json.get("categories_and_distribution", {}).get(
        "value", []
    )
    # categories_from_json should be either:
    #   - Movie (Theatrical Formatted for TV)
    #   - Episode and Episode C Type

    has_comcast_dmvpd = False
    has_directv_dmvpd = False
    has_charter_dmvpd = False
    has_verizon_dmvpd = False
    has_fubo_mvpd = False
    has_mvpd_affiliate_group = False

    for this_json_category in categories_from_json:
        this_json_cat_name = this_json_category.get("type", "")
        logger_service.info(
            'SUBSCRIPTIONS: working on category "{}"'.format(this_json_cat_name.upper())
        )
        this_json_partner_list = this_json_category.get("partners", [])
        if len(this_json_partner_list) == 0:
            continue

        for this_partner in this_json_partner_list:
            logger_service.info("> Partner: {}".format(this_partner))
            if this_partner == "Comcast Unified":
                has_comcast_dmvpd = True
            if this_partner == "DirecTV":
                has_directv_dmvpd = True
            if this_partner == "Charter" or this_partner == "Charter (via Deluxe)":
                has_charter_dmvpd = True
            if this_partner == "Verizon TVE":
                has_verizon_dmvpd = True
            if this_partner == "FuboTV":
                has_fubo_mvpd = True
            if this_partner == "MVPD Affiliate Group":
                has_mvpd_affiliate_group = True
            # TODO: add more partners to dmvp?

            if this_partner == "Comcast Unified":
                preroll_name = get_comcast_preroll_name_given_network(
                    content_json.get("network", {}).get("value", "")
                )
                # preroll_name = '5 Seconds Black'
            else:
                preroll_name = ""

            is_already_subscribed = reach_check_if_category_name_already_subscribed_to_partner_name_given_content_id(
                this_json_cat_name,
                this_partner,
                reach_content_id,
                reach_token,
                reach_address,
            )
            # try:
            if not is_already_subscribed:
                reach_subscribe_category_name_to_partner_name_given_content_id(
                    this_json_cat_name,
                    this_partner,
                    reach_content_id,
                    preroll_name,
                    reach_token,
                    reach_address,
                )
            else:
                reach_update_subscription_category_name_to_partner_name_given_content_id(
                    this_json_cat_name,
                    this_partner,
                    reach_content_id,
                    preroll_name,
                    reach_token,
                    reach_address,
                )

    # only worry about DMVPD mappings if we have a dMVPD partner (e.g. Comcast or DirecTV or Charter or MVPD Affiliate)
    if has_directv_dmvpd:
        logger_service.info("Working on DirecTV MVPD values")
        directv_partner_id = "1760"
        directv_mvpd_metadata_json = (
            reach_get_mvpd_data_for_partner_id_given_content_id(
                directv_partner_id, reach_content_id, reach_token, reach_address
            )
        )
        if is_movie is not True:
            truncate_series_name = content_json["content_name"]["value"]
            if ":" in truncate_series_name:
                truncate_series_name = truncate_series_name.replace(
                    ":", ""
                )  # no colons

            max_name_length = (
                21  # 21 characters + "/Season XX" = 31 characters. Just under max
            )
            if len(truncate_series_name) > max_name_length:
                if truncate_series_name[:2] == "A ":
                    truncate_series_name = truncate_series_name[2:]  # trim off "A "
                if truncate_series_name[:4] == "The ":
                    truncate_series_name = truncate_series_name[4:]  # trim off "The "

                if (
                    len(truncate_series_name) > max_name_length
                ):  # are we still over max?
                    truncate_series_name = truncate_series_name.replace(
                        " ", ""
                    )  # we need space - get rid of spaces

                truncate_series_name = truncate_series_name[
                    :max_name_length
                ]  # if still over max, truncate
            directv_mvpd_metadata_json["metadata"]["properties"][
                "direcTVSeriesShowFolder"
            ]["value"] = truncate_series_name
            reach_update_series_mvpd_data_for_partner_id_given_content_id(
                directv_mvpd_metadata_json,
                directv_partner_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

        if is_movie:  # automatically set to "Movies"
            directv_mvpd_metadata_json["metadata"]["properties"][
                "direcTVMovieSpecialShowFolder"
            ]["value"] = "Movies"
            reach_update_movie_mvpd_data_for_partner_id_given_content_id(
                directv_mvpd_metadata_json,
                directv_partner_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

        # notification_alerts.append('Need to add DirecTV dMVPD mappings')
        # logger_service.warning('Warning: DirecTV dMVPD support not added yet')

    # #####  COMCAST MVPD  #########
    logger_service.info(" > > > MVPD < < <")
    if has_comcast_dmvpd:
        logger_service.info("Working on Comcast MVPD values")
        # SD and HD mappings (Comcast)
        sd_mapping_list = content_json.get("sd_mappings", {}).get("value")
        if sd_mapping_list is None:
            sd_mapping_string = ""
        else:
            sd_mapping_string = "|".join(sd_mapping_list)

        hd_mapping_list = content_json.get("hd_mappings", {}).get("value")
        if hd_mapping_list is None:
            hd_mapping_string = ""
        else:
            hd_mapping_string = "|".join(hd_mapping_list)

        mvpd_show_folder = content_json.get("show_folder", {}).get("value")
        mvpd_title_brief = content_json.get("title_brief", {}).get("value")

        mvpd_genre_code = content_json.get("genre_code", {}).get(
            "value", ""
        )  # might be string, might be list
        if type(mvpd_genre_code) == list:
            mvpd_genre_code = "|".join(mvpd_genre_code)
        comcast_partner_id = "2200"
        comcast_mvpd_metadata_json = (
            reach_get_mvpd_data_for_partner_id_given_content_id(
                comcast_partner_id, reach_content_id, reach_token, reach_address
            )
        )

        # notifications
        if mvpd_genre_code.strip() == "":
            notification_alerts.append("Comcast dMVPD Genre code missing")
        if sd_mapping_string.strip() == "":
            notification_alerts.append("Comcast dMVPD SD Mapping are missing")
        if mvpd_genre_code.strip() == "":
            notification_alerts.append("Comcast dMVPD Genre code missing")
        if mvpd_genre_code.strip() == "":
            notification_alerts.append("Comcast dMVPD Genre code missing")

        if is_movie:
            # SD/HD mappings are part of metadata for movie/special
            comcast_mvpd_metadata_json["metadata"]["properties"][
                "comcastMovieSpecialGenreCodes"
            ]["value"] = mvpd_genre_code
            comcast_mvpd_metadata_json["metadata"]["properties"][
                "comcastMovieSpecialSDMappingStrings"
            ]["value"] = sd_mapping_string
            comcast_mvpd_metadata_json["metadata"]["properties"][
                "comcastMovieSpecialHDMappingStrings"
            ]["value"] = hd_mapping_string
            if mvpd_title_brief != "":
                comcast_mvpd_metadata_json["metadata"]["properties"][
                    "comcastMovieSpecialTitleBrief"
                ]["value"] = mvpd_title_brief
            if mvpd_show_folder != "":
                comcast_mvpd_metadata_json["metadata"]["properties"][
                    "comcastMovieSpecialShowFolder"
                ]["value"] = mvpd_show_folder

            reach_update_movie_mvpd_data_for_partner_id_given_content_id(
                comcast_mvpd_metadata_json,
                comcast_partner_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

        if is_movie is not True:
            # SD/HD mappings are part of the season
            # so first we do just the series metadata:
            comcast_mvpd_metadata_json["metadata"]["properties"][
                "comcastSeriesGenreCodes"
            ]["value"] = mvpd_genre_code
            if mvpd_title_brief != "":
                comcast_mvpd_metadata_json["metadata"]["properties"][
                    "comcastSeriesTitleBrief"
                ]["value"] = mvpd_title_brief
            if mvpd_show_folder != "":
                comcast_mvpd_metadata_json["metadata"]["properties"][
                    "comcastSeriesShowFolder"
                ]["value"] = mvpd_show_folder
            reach_update_series_mvpd_data_for_partner_id_given_content_id(
                comcast_mvpd_metadata_json,
                comcast_partner_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

            # second, update season mappings
            # note: season_id is the season id - from Season Creation earlier
            comcast_mvpd_season_json = reach_get_mvpd_season_given_season_id(
                season_id, comcast_partner_id, reach_token, reach_address
            )
            comcast_mvpd_season_json["metadata"]["properties"][
                "comcastSeriesSeasonSDMappingStrings"
            ]["value"] = sd_mapping_string
            comcast_mvpd_season_json["metadata"]["properties"][
                "comcastSeriesSeasonHDMappingStrings"
            ]["value"] = hd_mapping_string
            reach_update_season_mvpd_data_for_partner_id_given_season_id(
                comcast_mvpd_season_json,
                comcast_partner_id,
                season_id,
                reach_token,
                reach_address,
            )

    # ### VERIZON TVE MVPD ###
    if has_verizon_dmvpd:
        logger_service.info("Working on Verizon TVE MVPD values")
        verizon_partner_id = "2664"
        verizon_mvpd_metadata_json = (
            reach_get_mvpd_data_for_partner_id_given_content_id(
                verizon_partner_id, reach_content_id, reach_token, reach_address
            )
        )

        mvpd_genre_code = content_json.get("genre_code", {}).get(
            "value", ""
        )  # might be string, might be list
        if type(mvpd_genre_code) == list:  # if list, join the list with pipes
            mvpd_genre_code = "|".join(mvpd_genre_code)

        if mvpd_genre_code.strip() == "":
            notification_alerts.append("Verizon TVE MVPD Genre code missing")

        if is_movie is not True:
            if mvpd_genre_code != "":
                verizon_mvpd_metadata_json["metadata"]["properties"][
                    "verizonSeriesGenreCodes"
                ]["value"] = mvpd_genre_code
                reach_update_series_mvpd_data_for_partner_id_given_content_id(
                    verizon_mvpd_metadata_json,
                    verizon_partner_id,
                    reach_content_id,
                    reach_token,
                    reach_address,
                )

        if is_movie:
            if mvpd_genre_code != "":
                verizon_mvpd_metadata_json["metadata"]["properties"][
                    "verizonMovieSpecialGenreCodes"
                ]["value"] = mvpd_genre_code
                reach_update_movie_mvpd_data_for_partner_id_given_content_id(
                    verizon_mvpd_metadata_json,
                    verizon_partner_id,
                    reach_content_id,
                    reach_token,
                    reach_address,
                )

    # ### FUBO MVPD ###
    if has_fubo_mvpd:
        logger_service.info("Working on FuboTV MVPD values")
        fubo_partner_id = "3280"
        fubo_mvpd_metadata_json = reach_get_mvpd_data_for_partner_id_given_content_id(
            fubo_partner_id, reach_content_id, reach_token, reach_address
        )

        mvpd_genre_code = content_json.get("genre_code", {}).get(
            "value", ""
        )  # might be string, might be list
        if type(mvpd_genre_code) == list:  # if list, join the list with pipes
            mvpd_genre_code = "|".join(mvpd_genre_code)

        if mvpd_genre_code.strip() == "":
            notification_alerts.append("FuboTV MVPD Genre code missing")

        if is_movie is not True:
            if mvpd_genre_code != "":
                fubo_mvpd_metadata_json["metadata"]["properties"][
                    "fuboTVSeriesGenreCodes"
                ]["value"] = mvpd_genre_code
                reach_update_series_mvpd_data_for_partner_id_given_content_id(
                    fubo_mvpd_metadata_json,
                    fubo_partner_id,
                    reach_content_id,
                    reach_token,
                    reach_address,
                )

        if is_movie:
            if mvpd_genre_code != "":
                fubo_mvpd_metadata_json["metadata"]["properties"][
                    "fuboTVMovieSpecialGenreCodes"
                ]["value"] = mvpd_genre_code
                reach_update_movie_mvpd_data_for_partner_id_given_content_id(
                    fubo_mvpd_metadata_json,
                    fubo_partner_id,
                    reach_content_id,
                    reach_token,
                    reach_address,
                )

    # ### CHARTER MVPD ####
    if has_charter_dmvpd:
        logger_service.info("Working on Charter MVPD values")
        if "staging" in reach_address:
            charter_partner_id = "3501"  # Staging
        else:
            charter_partner_id = "3480"  # Prod
        charter_mvpd_metadata_json = (
            reach_get_mvpd_data_for_partner_id_given_content_id(
                charter_partner_id, reach_content_id, reach_token, reach_address
            )
        )

        mvpd_genre_code = content_json.get("genre_code", {}).get(
            "value", ""
        )  # might be string, might be list
        if type(mvpd_genre_code) == list:
            mvpd_genre_code = "|".join(mvpd_genre_code)

        if mvpd_genre_code.strip() == "":
            notification_alerts.append("Charter MVPD Genre code missing")

        if is_movie is not True:
            # update Genre Codes
            if mvpd_genre_code != "":
                charter_mvpd_metadata_json["metadata"]["properties"][
                    "charterSeriesGenreCodes"
                ]["value"] = mvpd_genre_code

            # update Series/Movie Categories
            truncate_series_name = content_json["content_name"]["value"]
            if ":" in truncate_series_name:
                truncate_series_name = truncate_series_name.replace(
                    ":", ""
                )  # no colons

            # note: Charter only gets FX and NG. They do not get FXX, FXM or NatGeo Wild. So ignore them.
            if truncate_series_name != "" and network_name != "":
                if "fx" in network_name.lower():
                    mvpd_category_str = (
                        "Primetime Free/"
                        + network_name
                        + "/"
                        + truncate_series_name[:25]
                    )
                elif "national geographic" in network_name.lower():
                    mvpd_category_str = (
                        "News Science Free/Nat Geo Channel/" + truncate_series_name[:25]
                    )
                charter_mvpd_metadata_json["metadata"]["properties"][
                    "charterSeriesMovieCategories"
                ]["value"] = mvpd_category_str

            # update Title Brief
            mvpd_title_brief = content_json.get("title_brief", {}).get("value")
            if mvpd_title_brief != "":
                charter_mvpd_metadata_json["metadata"]["properties"][
                    "charterSeriesTitleBrief"
                ]["value"] = mvpd_title_brief[:9]

            reach_update_series_mvpd_data_for_partner_id_given_content_id(
                charter_mvpd_metadata_json,
                charter_partner_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

        if is_movie:
            # update Genre Codes
            if mvpd_genre_code != "":
                charter_mvpd_metadata_json["metadata"]["properties"][
                    "charterMovieSpecialGenreCodes"
                ]["value"] = mvpd_genre_code

            # update Series/Movie Categories - nearly static values - nothing custom
            if network_name != "":
                mvpd_category_str = (
                    "Free Movies/" + network_name + "|Free Movies/Free Movies A-Z"
                )
                charter_mvpd_metadata_json["metadata"]["properties"][
                    "charterSeriesMovieCategories"
                ]["value"] = mvpd_category_str

            # update Title Brief
            mvpd_title_brief = content_json.get("title_brief", {}).get("value")
            if mvpd_title_brief != "":
                charter_mvpd_metadata_json["metadata"]["properties"][
                    "charterMovieSpecialTitleBrief"
                ]["value"] = mvpd_title_brief[:15]

            reach_update_movie_mvpd_data_for_partner_id_given_content_id(
                charter_mvpd_metadata_json,
                charter_partner_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

        # ### MVPD AFFILIATE GROUP (FOR VOD & VOD4) ####

        # Lots of notes to keep it clear:
        # MVPD items to be aware of:
        # 1) Provider (example "FX_HD" or "NATGEO_HD")
        # 2) Product Code (something like "MOD" or "MPT")
        # 3) Provider_Content_Tier (PCT) - examples "FX_PRIMETIME_FF_HD" or "NATIONALGEOGRAPHIC_HD_5"
        # 4) Category Mappings (ex: "TV Shows/By Network/FX/English Teacher HD" or "TV/TV Networks/Networks N-S/Nat Geo Wild/Critter Fixers HD")
        # 5) Genres (Comedy, Animals, Entertainment, etc)

        # Items 1-3 are at the Network level. And they are static. These don't change from series to series.
        # They should be manually added to the MVPD Network since they won't change once they are added.

        # Item 4 is at the Series>Season level or the main Movie level. This changes per Season (if series).
        # There can be multiple Category mappings

        # Item 5 is at the Series/Movie level
        # There can be multiple Genres

        # NOTE: Product Code, Provider and PCT are at the Network level. And pretty static.
        #       They should be manually added to the MVPD Network since they won't change once they are added.
    if has_mvpd_affiliate_group:
        logger_service.info("Working on MVPD Affiliate Group values")
        if "staging" in reach_address:
            mvpd_affiliate_id = "3560"  # Staging
        else:
            mvpd_affiliate_id = "3520"  # Prod

        # this gets the basic metadata json with empty fields
        # STEP 1: GET CONTENT MVPD PROPERTIES (in Reach UI: MVPD Setup > MVPD Affiliate Group > Series or Movie > Properties)
        #         This is done for both Series and Movies so we do it first. But we'll add data later depending on Series or Movie
        mvpd_affiliate_metadata_json = (
            reach_get_mvpd_data_for_partner_id_given_content_id(
                mvpd_affiliate_id, reach_content_id, reach_token, reach_address
            )
        )
        # Now start adding the Genre data (generally all affiliates use same genre codes)
        mvpd_genre_code = content_json.get("genre_code", {}).get(
            "value", ""
        )  # might be string, might be list
        if type(mvpd_genre_code) == list:
            mvpd_genre_code = "|".join(mvpd_genre_code)

        if mvpd_genre_code.strip() == "":
            notification_alerts.append("MVPD Affiliate Group - Genre code missing")

        # Work with affiliate-centric data
        # NOTE: each affiliate dataset will look like this:
        # aff_data = {'provider': '',
        #             'product_code': '',
        #             'pct': '',
        #             'category_mapping_list': [],
        #             'genre_list': []}

        # alta fiber
        affiliate_alta_fiber = affiliate_generate_mvpd_values_alta_fiber(
            content_json, is_movie
        )
        # altice
        affiliate_altice = affiliate_generate_mvpd_values_altice(content_json, is_movie)
        # armstrong
        affiliate_armstrong = affiliate_generate_mvpd_values_armstrong(
            content_json, is_movie
        )
        # bluestream
        affiliate_bluestream = affiliate_generate_mvpd_values_bluestream(
            content_json, is_movie
        )
        # breezeline
        affiliate_breezeline = affiliate_generate_mvpd_values_breezeline(
            content_json, is_movie
        )
        # buckeye
        affiliate_buckeye = affiliate_generate_mvpd_values_buckeye(
            content_json, is_movie
        )
        # cox
        affiliate_cox = affiliate_generate_mvpd_values_cox(content_json, is_movie)
        # c spire
        affiliate_cspire = affiliate_generate_mvpd_values_cspire(content_json, is_movie)
        # epb
        affiliate_epb = affiliate_generate_mvpd_values_epb(content_json, is_movie)
        # frontier
        affiliate_frontier = affiliate_generate_mvpd_values_frontier(
            content_json, is_movie
        )
        # gci
        affiliate_gci = affiliate_generate_mvpd_values_gci(content_json, is_movie)
        # hbc
        affiliate_hbc = affiliate_generate_mvpd_values_hbc(content_json, is_movie)
        # hotwire
        affiliate_hotwire = affiliate_generate_mvpd_values_hotwire(
            content_json, is_movie
        )
        # mctv
        affiliate_mctv = affiliate_generate_mvpd_values_mctv(content_json, is_movie)
        # mediacom
        affiliate_mediacom = affiliate_generate_mvpd_values_mediacom(
            content_json, is_movie
        )
        # mobi
        affiliate_mobi = affiliate_generate_mvpd_values_mobi(content_json, is_movie)
        # sectv
        affiliate_sectv = affiliate_generate_mvpd_values_sectv(content_json, is_movie)
        # sectv lehigh
        affiliate_sectvlehigh = affiliate_generate_mvpd_values_sectvlehigh(
            content_json, is_movie
        )
        # sectv sparta
        affiliate_sectvsparta = affiliate_generate_mvpd_values_sectvsparta(
            content_json, is_movie
        )
        # tangerine allo
        affiliate_tangerine_allo = affiliate_generate_mvpd_values_tangerine_allo(
            content_json, is_movie
        )
        # tds broadband
        affiliate_tds_broadband = affiliate_generate_mvpd_values_tds_broadband(
            content_json, is_movie
        )
        # tds telecom
        affiliate_tds_telecom = affiliate_generate_mvpd_values_tds_telecom(
            content_json, is_movie
        )
        # vubiquity wow
        affiliate_vubiquity_wow = affiliate_generate_mvpd_values_vubiquity_wow(
            content_json, is_movie
        )
        # verizon
        affiliate_verizon = affiliate_generate_mvpd_values_verizon(
            content_json, is_movie
        )
        # vermont telephone
        affiliate_vermont = affiliate_generate_mvpd_values_vermont(
            content_json, is_movie
        )

        if is_movie is not True:
            # first, update Series Genre Codes and Title Brief (remember, affiliates share these - so only one each)
            if mvpd_genre_code != "":
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "affiliateSeriesGenreCodes"
                ]["value"] = mvpd_genre_code
            # update Series Title Brief
            mvpd_title_brief = content_json.get("title_brief", {}).get("value")
            if mvpd_title_brief != "":
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "affiliateSeriesTitleBrief"
                ]["value"] = mvpd_title_brief[:9]
            # now actually update the data in Reach
            reach_update_series_mvpd_data_for_partner_id_given_content_id(
                mvpd_affiliate_metadata_json,
                mvpd_affiliate_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

            # second, update season mappings
            # note: season_id is the season id - from Season Creation earlier
            mvpd_affiliate_season_json = reach_get_mvpd_season_given_season_id(
                season_id, mvpd_affiliate_id, reach_token, reach_address
            )

            # Update all the different affiliate settings (almost every affiliate gets different category mapping)
            # Base Affiliate:
            mvpd_affiliate_season_json["metadata"]["properties"][
                "affiliateSeriesSeasonHDMappingStrings"
            ]["value"] = hd_mapping_string
            mvpd_affiliate_season_json["metadata"]["properties"][
                "affiliateSeriesSeasonSDMappingStrings"
            ]["value"] = sd_mapping_string

            # rinse and repeat for each affiliate:
            # alta fiber
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "altaFiberSeriesCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_alta_fiber.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning alta fiber MVPD values")

            # altice
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "cablevisionAlticeSeriesHDCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_altice.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Altice MVPD values")

            # armstrong
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "armstrongSeriesSeasonCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_armstrong.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Armstrong MVPD values")

            # bluestream
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "bluestreamSeriesCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_bluestream.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Bluestream MVPD values")

            # breezeline
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "breezelineSeriesCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_breezeline.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Breezeline MVPD values")

            # buckeye
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "buckeyeSeriesCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_buckeye.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Buckeye MVPD values")

            # cox (HD & SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "coxSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(affiliate_cox.get("category_mapping_list_hd", []))
            except:
                logger_service.error("problem assigning Cox HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "coxSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(affiliate_cox.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning Cox SD MVPD values")

            # C-Spire
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "cSpireSeriesSeasonCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_cspire.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning C-Spire MVPD values")

            # EPB (both HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "epbSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(affiliate_epb.get("category_mapping_list_hd", []))
            except:
                logger_service.error("problem assigning EPB HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "epbSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(affiliate_epb.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning EPB SD MVPD values")

            # frontier (both HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "frontierSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_frontier.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Frontier HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "frontierSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_frontier.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning Frontier SD MVPD values")

            # GCI (HD only)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "gciSeriesSeasonCategoryMapping"
                ]["value"] = "|".join(affiliate_gci.get("category_mapping_list_hd", []))
            except:
                logger_service.error("problem assigning GCI MVPD values")

            # HBC (HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "hbcSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(affiliate_hbc.get("category_mapping_list_hd", []))
            except:
                logger_service.error("problem assigning HBC HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "hbcSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(affiliate_hbc.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning HBC SD MVPD values")

            # Hotwire (HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "hotwireSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_hotwire.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Hotwire HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "hotwireSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_hotwire.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning Hotwire SD MVPD values")

            # MCTV (SD only)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "mctvSeriesSeasonCategoryMapping"
                ]["value"] = "|".join(affiliate_mctv.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning MCTV MVPD values")

            # Mediacom (HD only)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "mediacomSeriesSeasonCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_mediacom.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Mediacom MVPD values")

            # mobitv (HD only)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "mobiSeriesCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_mobi.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning MobiTV MVPD values")

            # SECTV (HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "sectvSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_sectv.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning SECTV HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "sectvSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(affiliate_sectv.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning SECTV SD MVPD values")

            # SECTV LehighValley (HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "sectvLehighSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvlehigh.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning SECTV Lehigh HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "sectvLehighSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvlehigh.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning SECTV Lehigh SD MVPD values")

            # SECTV Sparta (HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "sectvSpartaSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvsparta.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning SECTV Sparta HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "sectvSpartaSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvsparta.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning SECTV Sparta SD MVPD values")

            # tangerine allo (HD only)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "tangerineAlloSeriesCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_tangerine_allo.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning frontier MVPD values")

            # tds broadband (HD only)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "tdsBroadbandSeriesSeasonCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_tds_broadband.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning TDS Broadband HD MVPD values")

            # tds telecom (HD and SD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "tdsTelecomSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_tds_telecom.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning TDS Telecom HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "tdsTelecomSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_tds_telecom.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning TDS Telecom SD MVPD values")

            # vubiquity wow (HD only)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "vubiquityWOWSeriesCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_vubiquity_wow.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Vubiquity WOW MVPD values")

            # verizon (SD & HD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "verizonSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_verizon.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Verizon HD MVPD values")
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "verizonSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_verizon.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning Verizon SD MVPD values")

            # vermont telephone (SD & HD)
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "vermontSeriesSeasonCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_vermont.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error(
                    "problem assigning Vermont Telephone HD MVPD values"
                )
            try:
                mvpd_affiliate_season_json["metadata"]["properties"][
                    "vermontSeriesSeasonCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_vermont.get("category_mapping_list", [])
                )
            except:
                logger_service.error(
                    "problem assigning Vermont Telephone SD MVPD values"
                )

            # this will update the data in Reach
            reach_update_season_mvpd_data_for_partner_id_given_season_id(
                mvpd_affiliate_season_json,
                mvpd_affiliate_id,
                season_id,
                reach_token,
                reach_address,
            )

        if is_movie:
            # first update Genre Codes
            if mvpd_genre_code != "":
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "affiliateMovieSpecialGenreCodes"
                ]["value"] = mvpd_genre_code

            # update Title Brief
            mvpd_title_brief = content_json.get("title_brief", {}).get("value")
            if mvpd_title_brief != "":
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "affiliateMovieSpecialTitleBrief"
                ]["value"] = mvpd_title_brief[:15]

            # update Movie Categories
            mvpd_affiliate_metadata_json["metadata"]["properties"][
                "affiliateMovieSpecialHDMappingStrings"
            ]["value"] = hd_mapping_string
            mvpd_affiliate_metadata_json["metadata"]["properties"][
                "affiliateMovieSpecialSDMappingStrings"
            ]["value"] = sd_mapping_string

            # rinse and repeat for each affiliate:
            # alta fiber
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "altaFiberMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_alta_fiber.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning alta fiber MVPD values")

            # altice
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "cablevisionAlticeMovieHDCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_altice.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Altice MVPD values")

            # armstrong
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "armstrongMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_armstrong.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Armstrong MVPD values")

            # bluestream
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "bluestreamMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_bluestream.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Bluestream MVPD values")

            # breezeline
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "breezelineMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_breezeline.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Breezeline MVPD values")

            # buckeye
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "buckeyeMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_buckeye.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Buckeye MVPD values")

            # cox (HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "coxMovieCategoryHDMapping"
                ]["value"] = "|".join(affiliate_cox.get("category_mapping_list_hd", []))
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "coxMovieCategorySDMapping"
                ]["value"] = "|".join(affiliate_cox.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning Cox HD MVPD values")

            # C-Spire
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "cSpireMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_cspire.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning C-Spire MVPD values")

            # EPB (both HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "epbMovieCategoryHDMapping"
                ]["value"] = "|".join(affiliate_epb.get("category_mapping_list_hd", []))
            except:
                logger_service.error("problem assigning EPB HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "epbMovieCategorySDMapping"
                ]["value"] = "|".join(affiliate_epb.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning EPB SD MVPD values")

            # frontier (both HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "frontierMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_frontier.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Frontier HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "frontierMovieCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_frontier.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning Frontier SD MVPD values")

            # GCI (HD only)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "gciMovieCategoryMapping"
                ]["value"] = "|".join(affiliate_gci.get("category_mapping_list_hd", []))
            except:
                logger_service.error("problem assigning GCI MVPD values")

            # HBC (HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "hbcMovieCategoryHDMapping"
                ]["value"] = "|".join(affiliate_hbc.get("category_mapping_list_hd", []))
            except:
                logger_service.error("problem assigning HBC HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "hbcMovieCategorySDMapping"
                ]["value"] = "|".join(affiliate_hbc.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning HBC SD MVPD values")

            # Hotwire (HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "hotwireMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_hotwire.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Hotwire HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "hotwireMovieCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_hotwire.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning Hotwire SD MVPD values")

            # MCTV (SD only)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "mctvMovieCategoryMapping"
                ]["value"] = "|".join(affiliate_mctv.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning MCTV MVPD values")

            # Mediacom (HD only)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "mediacomMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_mediacom.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Mediacom MVPD values")

            # mobitv (HD only)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "mobiMovieCategoryMapping"
                ]["value"] = "|".join(affiliate_mobi.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning MobiTV MVPD values")

            # SECTV (HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "sectvMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_sectv.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning SECTV HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "sectvMovieCategorySDMapping"
                ]["value"] = "|".join(affiliate_sectv.get("category_mapping_list", []))
            except:
                logger_service.error("problem assigning SECTV SD MVPD values")

            # SECTV LehighValley (HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "sectvLehighMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvlehigh.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning SECTV Lehigh HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "sectvLehighMovieCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvlehigh.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning SECTV Lehigh SD MVPD values")

            # SECTV Sparta (HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "sectvSpartaMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvsparta.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning SECTV Sparta HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "sectvSpartaMovieCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_sectvsparta.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning SECTV Sparta SD MVPD values")

            # tangerine allo (HD only)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "tangerineAlloMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_tangerine_allo.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning frontier MVPD values")

            # tds broadband (HD only)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "tdsBroadbandMovieCategoryMapping"
                ]["value"] = "|".join(
                    affiliate_tds_broadband.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning TDS Broadband HD MVPD values")

            # tds telecom (HD and SD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "tdsTelecomMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_tds_telecom.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning TDS Telecom HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "tdsTelecomMovieCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_tds_telecom.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning TDS Telecom SD MVPD values")

            # vubiquity wow (HD only)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "vubiquityWOWSeriesCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_vubiquity_wow.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Vubiquity WOW MVPD values")

            # verizon (SD & HD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "verizonMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_verizon.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error("problem assigning Verizon HD MVPD values")
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "verizonMovieCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_verizon.get("category_mapping_list", [])
                )
            except:
                logger_service.error("problem assigning Verizon SD MVPD values")

            # vermont telephone (SD & HD)
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "vermontMovieCategoryHDMapping"
                ]["value"] = "|".join(
                    affiliate_vermont.get("category_mapping_list_hd", [])
                )
            except:
                logger_service.error(
                    "problem assigning Vermont Telephone HD MVPD values"
                )
            try:
                mvpd_affiliate_metadata_json["metadata"]["properties"][
                    "vermontMovieCategorySDMapping"
                ]["value"] = "|".join(
                    affiliate_vermont.get("category_mapping_list", [])
                )
            except:
                logger_service.error(
                    "problem assigning Vermont Telephone SD MVPD values"
                )

            reach_update_movie_mvpd_data_for_partner_id_given_content_id(
                mvpd_affiliate_metadata_json,
                mvpd_affiliate_id,
                reach_content_id,
                reach_token,
                reach_address,
            )

    # TODO: have a function check through the series/movie, everything and check for specific items/problems
    #       add anything found to the Slack notification

    # #######################################
    #          PACKAGE CREATION
    # #######################################
    # create package if necessary
    logger_service.info(" > > > PACKAGE < < <")
    episode_title = content_json.get("episode_title", {}).get("value", "")
    if episode_title == "":
        return

    material_id = content_json.get("material_id", {}).get("value", "")

    # see if package already exists:
    existing_packages = reach_search_for_existing_package(
        content_json, reach_token, reach_address
    )
    if existing_packages == "error":
        logger_service.error("Error checking for existing packages. Stopping early.")
        return

    new_pkg_category = content_json.get("episode_category", {}).get("value", "")
    if (
        "episode c type" == new_pkg_category.lower()
        or "special c type" == new_pkg_category.lower()
        or "movie c type" == new_pkg_category.lower()
    ):
        is_c_type = True
    else:
        is_c_type = False

    is_new_pkg = True

    if len(existing_packages) == 0:
        # no packages found - so we create it
        logger_service.info("No existing package found - trying to create one")
        try:
            new_pkg = reach_create_package(content_json, reach_token, reach_address)
            new_pkg_id = new_pkg.get("id", "")
            logger_service.info("New package created: {}".format(new_pkg_id))
        except Exception as e:
            logger_service.error(e)
            return

    elif len(existing_packages) > 2:
        # this might be bad logic - will adjust in the future if necessary
        logger_service.error("More than 2 matching packages found - stopping early.")
        return

    else:
        # we found 1-2 pkgs - prob Episode and Episode C Type
        # does this pkg match the category we are trying to fulfill?
        new_pkg_id = ""
        for this_ext_pkg in existing_packages:
            # exist_pkg = existing_packages[0]
            ###exist_pkg = this_ext_pkg
            exist_pkg_cat: str = this_ext_pkg.get("category", {}).get("name", "")
            exist_pkg_id = this_ext_pkg.get("id", "")

            if exist_pkg_cat.lower().strip() == new_pkg_category.lower().strip():
                logger_service.info(
                    "Package already exists - will update existing package: {}".format(
                        exist_pkg_id
                    )
                )
                new_pkg_id = this_ext_pkg.get("id", "")
                new_pkg = this_ext_pkg
                is_new_pkg = False
                break

        if new_pkg_id == "":
            # pkg with category not found - so we create it
            logger_service.info(
                'No existing package of type "{}" found - trying to create one'.format(
                    new_pkg_category
                )
            )
            try:
                new_pkg = reach_create_package(content_json, reach_token, reach_address)
                new_pkg_id = new_pkg.get("id", "")
                logger_service.info("New package created: {}".format(new_pkg_id))
            except Exception as e:
                logger_service.error(e)
                return

    # except:
    #     logger_service.error('Problem creating package')
    #     return

    if new_pkg_id == "":
        raise Exception("Problem with newly created package.")

    # for tracking if we have changes:
    pkg_json_has_been_updated = False
    logger_service.info("Now seeing if we can further update the package metadata")
    # add category to pkg json
    all_pkg_categories = reach_get_package_categories_given_package_id(
        new_pkg_id, reach_token, reach_address
    )
    for this_pkg_category in all_pkg_categories:
        this_pkg_cat_name = this_pkg_category.get("name", "")
        if (
            this_pkg_cat_name.lower()
            == content_json.get("episode_category", {}).get("value", "").lower()
        ):
            new_pkg["category"] = this_pkg_category
            pkg_json_has_been_updated = True
            break

    # keywords
    if content_json.get("keywords", {}).get("value") != "":
        new_pkg["keywords"] = content_json.get("keywords", {}).get("value")
        pkg_json_has_been_updated = True

    # short description
    if content_json.get("episode_short_synopsis", {}).get("value") != "":
        new_pkg["shortDescription"] = content_json.get(
            "episode_short_synopsis", {}
        ).get("value")
        pkg_json_has_been_updated = True

    # long description
    if content_json.get("episode_long_synopsis", {}).get("value") != "":
        new_pkg["longDescription"] = content_json.get("episode_long_synopsis", {}).get(
            "value"
        )
        pkg_json_has_been_updated = True

    # originalReleaseDate and air time - take the episode_airdate value, split into day and time (24-hour)
    episode_airdate = content_json.get("episode_airdate", {}).get("value", "")
    try:
        # episode_airdate_dt = datetime.strptime(episode_airdate, "%Y-%m-%dT%H:%M:%SZ")
        episode_airdate_utc = dtparser.parse(episode_airdate)
        # episode_airdate_dt.replace(tzinfo=tz.tzutc())  # specify UTC timezone
        eastern_tz = tz.gettz("America/New_York")
        # pacific_tz = tz.gettz("America/Los_Angeles")
        episode_airdate_localized = episode_airdate_utc.astimezone(eastern_tz)
        episode_airdate_time = episode_airdate_localized.strftime("%H:%M")

        # date needs to be the day of air at midnight + 8 hours for GMT offset
        # example: Feb 8, 2024 should be "2024-02-08T08:00:00.000+0000"
        airdate_year = int(episode_airdate_localized.strftime("%Y"))
        airdate_month = int(episode_airdate_localized.strftime("%m"))
        airdate_day = int(episode_airdate_localized.strftime("%d"))

        # calculate if the airdate is during daylight saving time (March 2nd sunday to November 1st sunday)
        # see what day of the week is March 1, count days until Sunday. Then add 7 more to get 2nd Sunday
        # similar to November, but don't add extra 7 days to get 1st Sunday
        mar1 = datetime(airdate_year, 3, 1)
        second_sunday_in_march = mar1 + timedelta((6 - mar1.weekday()) + 7)
        nov1 = datetime(airdate_year, 11, 1)
        first_sunday_in_november = nov1 + timedelta(6 - nov1.weekday())

        dst_in_effect = False
        # if apr to oct, definitely in DST
        # if March and after 2nd sunday (including Sunday because 3am onward=in DST), then yes in DST
        # if Nov and before 1st Sunday (excluding that Sunday), then yes in DST

        # NOTE: strfime values are not all portable between Mac/Linux and Windows
        # Example: strftime('%-d') works fine on Mac/Linux, but breaks on Windows
        #          On Windows, use strftime('%#d')
        curr_operating_system = sys.platform
        if curr_operating_system.lower() == "win32":
            if 3 < int(airdate_month) < 11:
                dst_in_effect = True
            elif int(airdate_month) == 3 and (
                int(airdate_day) >= int(second_sunday_in_march.strftime("%#d"))
            ):
                dst_in_effect = True
            elif int(airdate_month) == 11 and (
                int(airdate_day) < int(first_sunday_in_november.strftime("%#d"))
            ):
                dst_in_effect = True
        else:
            if 3 < int(airdate_month) < 11:
                dst_in_effect = True
            elif int(airdate_month) == 3 and (
                int(airdate_day) >= int(second_sunday_in_march.strftime("%-d"))
            ):
                dst_in_effect = True
            elif int(airdate_month) == 11 and (
                int(airdate_day) < int(first_sunday_in_november.strftime("%-d"))
            ):
                dst_in_effect = True

        if dst_in_effect:
            episode_airdate_date = (
                episode_airdate_localized.strftime("%Y-%m-%d")
            ) + "T07:00:00.000+0000"
        else:
            episode_airdate_date = (
                episode_airdate_localized.strftime("%Y-%m-%d")
            ) + "T08:00:00.000+0000"

        new_pkg["originalReleaseDate"] = episode_airdate_date
        new_pkg["airTime"] = episode_airdate_time

    except:
        logger_service.warning('Problem parsing airdate "{}"'.format(episode_airdate))

    # primary language
    # just assume English
    language_json = reach_get_language()
    new_pkg["primaryLanguage"] = language_json

    # production number
    if content_json.get("episode_production_number", {}).get("value") != "":
        new_pkg["productionNumber"] = content_json.get(
            "episode_production_number", {}
        ).get("value")
        pkg_json_has_been_updated = True

    # episode number
    if content_json.get("episode_number", {}).get("value") != "":
        new_pkg["sequenceNumber"] = content_json.get("episode_number", {}).get("value")
        pkg_json_has_been_updated = True

    # season
    if is_movie is False:
        season_num = content_json.get("season_num", {}).get("value", "")
        all_pkg_seasons = reach_get_seasons_given_package_id(
            new_pkg_id, reach_token, reach_address
        )
        for this_pkg_season in all_pkg_seasons:
            this_pkg_season_value = this_pkg_season.get("value", "")
            if this_pkg_season_value.strip() == season_num.strip():
                new_pkg["season"] = this_pkg_season
                pkg_json_has_been_updated = True
                break

    # TMS ID
    if content_json.get("episode_tms_id", {}).get("value") != "":
        new_pkg["tMSId"] = content_json.get("episode_tms_id", {}).get("value")
        pkg_json_has_been_updated = True

    # Ratings
    all_ratings = reach_get_ratings(reach_token, reach_address)
    episode_rating = content_json.get("default_rating", {}).get("value", "")
    for this_rating in all_ratings:
        this_rating_name = this_rating.get("name")
        if this_rating_name == episode_rating:
            new_pkg["tVRating"] = this_rating
            pkg_json_has_been_updated = True

    # Rating Descriptors
    descriptors_to_add = []
    all_descriptors_in_reach = reach_get_rating_descriptors(reach_token, reach_address)
    episode_rating_descriptors = content_json.get("rating_content_labels", {}).get(
        "value", []
    )
    for this_episode_descriptor in episode_rating_descriptors:
        for this_reach_descriptor in all_descriptors_in_reach:
            this_reach_descriptor_name = this_reach_descriptor.get("name", "")
            if this_reach_descriptor_name.lower() == this_episode_descriptor.lower():
                descriptors_to_add.append(this_reach_descriptor)
                break

    if len(descriptors_to_add) > 0:
        new_pkg["tVRatingDescriptors"] = descriptors_to_add
        pkg_json_has_been_updated = True

    # Title
    if episode_title != "":
        new_pkg["title"] = episode_title
        pkg_json_has_been_updated = True

    # Is this 4K?
    is_4k = content_json.get("is_4k", {}).get("value", False)

    # Add C3 or Clean to Nickname
    # new_pkg_category will be "Episode" or "Episode C Type", "Special" or "Special C Type"
    nickname_prefix = content_json.get("content_prefix", {}).get("value", "")
    if is_movie is False:
        nickname_season_num = content_json.get("season_num", {}).get("value", "")
        if nickname_season_num is None or nickname_season_num == "":
            nickname_season_num = ""
        nickname_episode_num = content_json.get("episode_number", {}).get("value", "")
        if nickname_episode_num is None or nickname_episode_num == "":
            nickname_episode_num = "__"
        nickname_season_ep = (
            " " + nickname_season_num + str(nickname_season_num).zfill(2) + " "
        )
    else:
        nickname_season_ep = " "

    # TODO: add series prefix and episode ID before episode_title. Pre-req: able to create prefixes
    pkg_type_short = ""
    if "episode c type" == new_pkg_category.lower():
        # episode_title_and_category = episode_title + ' (C3) ' + material_id
        episode_title_and_category = (
            nickname_prefix
            + nickname_season_ep
            + episode_title
            + " (C3) "
            + material_id
        )
        pkg_type_short = "(C3)"
    elif "episode" == new_pkg_category.lower():
        # episode_title_and_category = episode_title + ' (Clean) ' + material_id
        if is_4k:
            episode_title_and_category = (
                nickname_prefix
                + nickname_season_ep
                + episode_title
                + " (Clean 4K) "
                + material_id
            )
            pkg_type_short = "(Clean 4K)"
        else:
            episode_title_and_category = (
                nickname_prefix
                + nickname_season_ep
                + episode_title
                + " (Clean) "
                + material_id
            )
            pkg_type_short = "(Clean)"
    elif "special" == new_pkg_category.lower():
        episode_title_and_category = (
            nickname_prefix + " " + episode_title + " (Special) " + material_id
        )
        pkg_type_short = "(Special)"
    elif "special c type" == new_pkg_category.lower():
        episode_title_and_category = (
            nickname_prefix + " " + episode_title + " (Special C3) " + material_id
        )
        pkg_type_short = "(Special C3)"
    elif "movie c type" == new_pkg_category.lower():
        episode_title_and_category = (
            nickname_prefix + " " + episode_title + " (Movie C3) " + material_id
        )
        pkg_type_short = "(Movie C3)"
    elif "episode d type" == new_pkg_category.lower():
        episode_title_and_category = (
            nickname_prefix + " " + episode_title + " (VOD) " + material_id
        )
        pkg_type_short = "(VOD)"
    elif "movie d type" == new_pkg_category.lower():
        episode_title_and_category = (
            nickname_prefix + " " + episode_title + " (Movie VOD) " + material_id
        )
        pkg_type_short = "(Movie VOD)"
    else:
        episode_title_and_category = (
            nickname_prefix + " " + episode_title + " " + material_id
        )
        pkg_type_short = "(Movie)"  # guessing that it's a movie

    new_pkg["name"] = episode_title_and_category
    # set traffic code at metadata/properties/trafficCode/value

    try:
        pkg_item = new_pkg["metadata"]
    except KeyError:
        new_pkg["metadata"] = {}
    try:
        pkg_item = new_pkg["metadata"]["properties"]
    except KeyError:
        new_pkg["metadata"]["properties"] = {}

    try:
        pkg_item = new_pkg["metadata"]["properties"]["trafficCode"]
    except KeyError:
        new_pkg["metadata"]["properties"]["trafficCode"] = {}

    new_pkg["metadata"]["properties"]["trafficCode"]["value"] = material_id

    #  now post the new_pkg to update the package
    if pkg_json_has_been_updated:
        logger_service.info(
            "Package metadata json is ready. Now going to use this data to update package in Reach."
        )
        time_before_update = datetime.now()
        updated_pkg_json = reach_update_package_given_pkg_json(
            new_pkg, reach_token, reach_address
        )
        time_after_update = datetime.now()
        total_time_to_update = time_after_update - time_before_update
        formatted_time = str(total_time_to_update.total_seconds())

        logger_service.info(
            "Package is now updated. Time to update: {}".format(formatted_time)
        )

    # ######################################################
    #          PARTNER PACKAGES (aka DISTRIBUTION GRID)
    # ######################################################
    logger_service.info(" > > > DIST GRID (PARTNER PACKAGES) < < <")
    logger_service.info(
        "bankable_date = {}".format(
            content_json.get("bankable_date", {}).get("value", "")
        )
    )
    # get current dist grid values
    # loop through - see if partner name is on our list. If so, update a sub-dataset for a PUT to update dist grid windows
    package_current_dist_grid_json = reach_get_distribution_grid_given_package_id(
        new_pkg_id, reach_token, reach_address
    )
    new_dist_grid_subset = []
    dist_grid_has_changed = False

    episode_window_start = content_json.get("episode_window_start", {}).get("value", "")
    if episode_window_start == "":
        return

    episode_window_end = content_json.get("episode_window_end", {}).get("value", "")
    if episode_window_end == "":
        return

    episode_window_start_utc = dtparser.parse(episode_window_start)
    episode_window_end_utc = dtparser.parse(episode_window_end)
    # eastern_tz = tz.gettz('America/New_York')
    # pacific_tz = tz.gettz('America/Los_Angeles') # old timezone
    pacific_tz = tz.gettz(
        "America/New_York"
    )  # changed 9/6/2024 but keeping same variable name so fewer script changes

    episode_window_start_localized = episode_window_start_utc.astimezone(pacific_tz)
    episode_window_end_localized = episode_window_end_utc.astimezone(pacific_tz)
    # episode_airdate_time = episode_airdate_localized.strftime('%H:%M')
    # Common list of partners:
    # common_partners = ['ANET',
    #                    'Charter',
    #                    'Comcast Unified',
    #                    'DirecTV',
    #                    'DISH',
    #                    'DTCI Video Platforms',
    #                    'FuboTV',
    #                    'Genome',
    #                    'Hulu',
    #                    'Hulu dMVPD',
    #                    'Verizon TVE',
    #                    'Vidgo',
    #                    'YouTube dMVPD']

    is_acquired = content_json.get("is_acquired", {}).get("value", False)

    for this_dist_grid_partner in package_current_dist_grid_json:
        this_partner_name = this_dist_grid_partner.get("partner", {}).get("name", "")
        this_partner_instance_id = this_dist_grid_partner.get("id", "")
        if this_partner_instance_id == "" or this_partner_name == "":
            continue

        if this_partner_name == "ANET":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_anet(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: ANET dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if (
            this_partner_name == "Charter"
            or this_partner_name == "Charter (via Deluxe)"
        ):
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_charter(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: Charter dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "Comcast Unified":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_comcast(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: Comcast dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "DirecTV":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                # episode_window_end = datetime.strftime(bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z")
                # DirecTV uses "DateField" metadata - not "DateTimeField" so we have to change the format.
                episode_window_end = (
                    datetime.strftime(
                        bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f"
                    )[:-3]
                    + "Z"
                )
            temp_subset = partner_get_dist_grid_directv(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: DirecTV dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "DISH":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_dish(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: DISH dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "DTCI Video Platforms":
            # MediaHub only provides a single window
            # Per Keenan BurtonSessoms (Disney Multiplatform Programming):
            #  "The unauthenticated (free) start date is always 30 days after the linear premiere date.
            #  The end date stays the end date that is now send via mediahub"
            #  - note: the above rules from Keenan only apply to NGC/NGW (per Jim Donnelly)
            #
            # Update per Jonathan via email "FXNG Windowing Dates for Sites and Apps" May 31, 2024:
            # Paid is fine. Just use same window provided from MediaHub
            # Free Sunrise is Paid sunset + 1 minute
            # Free Sunset is Bankable date
            # Update 2: per meeting 10/31/2024:
            # Paid Start = NLDTypes:StartDate
            # Paid End = Paid Start + 30 days
            # Free Start = Paid End + 1 minute
            # Free End = Bankable date
            no_window = "3000-01-01T00:00:00.000+0000"

            if "fx" in network_name.lower():
                dtci_paid_window_end = episode_window_end
            else:  # NGC or NGW
                # episode_airdate = content_json.get('episode_airdate', {}).get('value', '')
                thirty_day_delta = timedelta(days=30)
                paidstart_plus_30 = episode_window_start_localized + thirty_day_delta
                # keep the end window times
                paidstart_plus_30 = paidstart_plus_30.replace(
                    hour=episode_window_end_localized.hour,
                    minute=episode_window_end_localized.minute,
                )
                dtci_paid_window_end = datetime.strftime(
                    paidstart_plus_30, "%Y-%m-%dT%H:%M:%S.%f%z"
                )

            # airdate_plus_30 = episode_airdate_localized + thirty_day_delta
            # if airdate_plus_30 > episode_window_end_localized:  # 30 days past airdate - if this is after the window end, just no free window
            #     free_window_start = no_window
            #     free_window_end = no_window
            # else:
            #     free_window_start = datetime.strftime(airdate_plus_30, "%Y-%m-%dT%H:%M:%S.%f%z")
            #     free_window_end = episode_window_end
            # #### ignore this ^ ^ ^ ^ ^ ^

            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if (
                bankable_date == "" or bankable_date is None
            ) or "fx" in network_name.lower():
                free_window_start = no_window
                free_window_end = no_window
            else:
                # free_window_start_dt = episode_window_end_localized + timedelta(minutes=1)
                free_window_start_dt = paidstart_plus_30 + timedelta(minutes=1)

                free_window_end_utc = dtparser.parse(bankable_date)
                free_window_end_localized = free_window_end_utc.astimezone(pacific_tz)

                if free_window_start_dt > free_window_end_localized:
                    # paid window start is after bankable - this means there is no free window
                    free_window_start = no_window
                    free_window_end = no_window
                else:
                    free_window_start = datetime.strftime(
                        free_window_start_dt, "%Y-%m-%dT%H:%M:%S.%f%z"
                    )
                    free_window_end = datetime.strftime(
                        free_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                    )

            temp_subset = partner_get_dist_grid_dtci(
                this_partner_instance_id,
                episode_window_start,
                dtci_paid_window_end,
                free_window_start,
                free_window_end,
            )
            # add house ID
            temp_subset["metadata"]["properties"]["houseID"] = {"value": material_id}

            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: DTCI Paid dates: {}  -  {}".format(
                    episode_window_start, dtci_paid_window_end
                )
            )
            logger_service.info(
                "Dist Grid: DTCI Free dates: {}  -  {}".format(
                    free_window_start, free_window_end
                )
            )
            continue

        if this_partner_name == "FuboTV":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )

            temp_subset = partner_get_dist_grid_fubotv(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: FuboTV dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "Genome":
            # Genome doesn't have windows - it's a Disney internal platform
            continue

        if this_partner_name == "Hulu":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_hulu(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: Hulu SVOD dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "Hulu dMVPD":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_hulu_dmvpd(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: Hulu dMVPD dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "MVPD Affiliate Group":
            # override the window end if there is a bankable date
            # bankable_date = content_json.get('bankable_date', {}).get('value', '')
            bankable_date = ""  # skip bankable for VOD or VOD4
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                # episode_window_end = datetime.strftime(bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z") # this is wrong - outputting something like '2025-09-17T23:59:59.000000-0400'
                # YouTube uses "DateField" metadata - not "DateTimeField" so we have to change the format.
                episode_window_end = (
                    datetime.strftime(
                        bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f"
                    )[:-3]
                    + "Z"
                )  # this should output a value of something like '2024-09-27T10:00:00.000Z'
            temp_subset = partner_get_dist_grid_dmvpd_affiliate_group(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: MVPD Affiliate Group dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "Verizon TVE":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_verizon(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: Verizon dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "Vidgo":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                episode_window_end = datetime.strftime(
                    bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z"
                )
            temp_subset = partner_get_dist_grid_vidgo(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: Vidgo dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue

        if this_partner_name == "YouTube dMVPD":
            # override the window end if there is a bankable date
            bankable_date = content_json.get("bankable_date", {}).get("value", "")
            if bankable_date == "" or bankable_date is None:
                pass
            elif not is_c_type:
                bank_window_end_utc = dtparser.parse(bankable_date)
                bank_window_end_localized = bank_window_end_utc.astimezone(pacific_tz)
                # episode_window_end = datetime.strftime(bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f%z") # this is wrong - outputting something like '2025-09-17T23:59:59.000000-0400'
                # YouTube uses "DateField" metadata - not "DateTimeField" so we have to change the format.
                episode_window_end = (
                    datetime.strftime(
                        bank_window_end_localized, "%Y-%m-%dT%H:%M:%S.%f"
                    )[:-3]
                    + "Z"
                )  # this should output a value of something like '2024-09-27T10:00:00.000Z'
            temp_subset = partner_get_dist_grid_youtube_dmvpd(
                this_partner_instance_id,
                new_pkg_category,
                c_win_length,
                is_acquired,
                episode_window_start,
                episode_window_end,
            )
            new_dist_grid_subset.append(temp_subset)
            dist_grid_has_changed = True
            logger_service.info(
                "Dist Grid: YouTube dates: {}  -  {}".format(
                    episode_window_start, episode_window_end
                )
            )
            continue
            # partner_get_dist_grid_dmvpd_affiliate_group

    if dist_grid_has_changed:
        logger_service.info("updated dist grid json:")
        logger_service.info(json.dumps(new_dist_grid_subset))
        update_response = reach_update_distribution_grid_given_package_id(
            new_pkg_id, new_dist_grid_subset, reach_token, reach_address
        )

    # Update External ingest
    # material_id = content_json.get('material_id', {}).get('value', '')
    if material_id == "":
        logger_service.error("material_id is missing in JSON")
    else:
        if "episode c type" in new_pkg_category.lower():
            pkg_type = "_C3"
        elif "episode" == new_pkg_category.lower():
            pkg_type = "_Clean"
        elif "episode d type" == new_pkg_category.lower():
            pkg_type = "_D4"
        elif "movie (theatrical formatted for tv)" == new_pkg_category.lower():
            pkg_type = "_Movie"
        elif "movie c type" == new_pkg_category.lower():
            pkg_type = "_MovieC3"
        elif "movie d type" == new_pkg_category.lower():
            pkg_type = "_MovieD4"
        elif "special" == new_pkg_category.lower():
            pkg_type = "_Special"
        elif "special c type" == new_pkg_category.lower():
            pkg_type = "_SpecialC3"
        elif "special d type" == new_pkg_category.lower():
            pkg_type = "_SpecialD4"
        else:
            logger_service.error(
                'Unable to determine episode category (NLD type): "{}"'.format(
                    new_pkg_category
                )
            )
            pkg_type = ""

        if pkg_type != "":
            material_id_and_type = material_id + pkg_type
            logger_service.info(
                'Trying to update pkg {0} with External ID "{1}"'.format(
                    new_pkg_id, material_id_and_type
                )
            )
            ext_success = reach_update_package_external_id_and_system_name(
                new_pkg_id,
                material_id_and_type,
                "FXNG_from_MediaHub",
                reach_token,
                reach_address,
            )
            if ext_success:
                logger_service.info("Successfully updated package External ID")
            else:
                logger_service.error("Failed updating package External ID")

    #
    # Last check - get package errors
    pkg_errors = reach_get_package_errors_given_package_id(
        new_pkg_id, reach_token, reach_address
    )
    print("Notifications for Slack:")
    print("\n".join(notification_alerts))
    pkg_url = reach_address + "/packages/package/edit/{0}/metadata".format(
        new_pkg_id
    )  # web URL, not API
    if is_movie:
        short_size_limit = 35
    else:  # will be series and episode title - make it shorter
        short_size_limit = 20

    if len(content_json["content_name"]["value"]) > short_size_limit:
        content_name_short = (content_json["content_name"]["value"])[
            :short_size_limit
        ] + "..."
    else:
        content_name_short = content_json["content_name"]["value"]

    if len(episode_title) > short_size_limit:
        episode_name_short = episode_title[:short_size_limit] + "..."
    else:
        episode_name_short = episode_title

    if len(notification_alerts) > 0 or len(pkg_errors) > 0:
        pkg_summary = "the following errors need fixed before delivery:"
    else:
        # no problems
        if is_new_pkg:
            pkg_summary = "package has been created. No errors detected. :hello_racoon:"
        else:
            pkg_summary = "package has been updated. No errors detected. :hello_racoon:"

    if is_movie:
        slack_full_msg = (
            ":rocket-raccoon-emoji: *RASCL Notification*\n"
            + 'Package <{0}|{1}> *[{2}]  [ _"{3}" {4}_ ]*: {5}\n'.format(
                pkg_url,
                new_pkg_id,
                material_id,
                episode_name_short,
                pkg_type_short,
                pkg_summary,
            )
            + "\n".join(notification_alerts)
            + "\n"
            + "\n".join(pkg_errors)
        )

    else:
        slack_full_msg = (
            ":rocket-raccoon-emoji: *RASCL Notification*\n"
            + 'Package <{0}|{1}> *[{2}]  [ _{3} - "{4}" {5}_ ]*: {6}\n'.format(
                pkg_url,
                new_pkg_id,
                material_id,
                content_name_short,
                episode_name_short,
                pkg_type_short,
                pkg_summary,
            )
            + "\n".join(notification_alerts)
            + "\n"
            + "\n".join(pkg_errors)
        )

    send_slack_msg(slack_full_msg, env)


def load_to_reach(
    data, artwork=None, smallartwork=None, vodartwork=None, settings=None, env="sbx"
):
    logger_service.info("STARTING - JSON file: {}".format(data))
    content_json = data

    config_dict = config_parse(settings)

    if env == "prod":
        reach_address = config_dict["reach_prod_address"]
    else:
        reach_address = config_dict["reach_staging_address"]

    if reach_address[:8] != "https://":
        reach_address = "https://" + reach_address

    reach_engine_login = config_dict["reach_engine_login"]
    reach_engine_password = config_dict["reach_engine_password"]

    reach_token = reach_login(reach_engine_login, reach_engine_password, reach_address)
    start_time = datetime.now()
    main(content_json, artwork, smallartwork, reach_token, reach_address, env)
    end_time = datetime.now()
    total_time = end_time - start_time
    total_time_str = str(total_time.total_seconds())
    logger_service.info(
        "FINISHED (work completed in {}s) - JSON file: {}".format(total_time_str, data)
    )
    return True
