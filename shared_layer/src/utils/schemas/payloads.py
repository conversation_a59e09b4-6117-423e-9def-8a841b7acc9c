from typing import Any, Dict, List, Literal, Optional, Union
from datetime import datetime

from pydantic import AliasPath, BaseModel, ConfigDict, Field, field_validator, model_validator

from ..LoggerService import logger_service


class WonderlandSidecarSchema(BaseModel):
    """Schema for Wonderland sidecar JSON payload."""

    model_config = ConfigDict(extra="ignore")

    version: int
    sidecar_id: str
    asset_version: str
    asset_format: Literal["single_file", "multi_file"]
    asset_id: str
    reach_package_id: str


class BebanjoPayloadSchema(BaseModel):
    """Schema for Bebanjo payload JSON that maps to RASCL adi_json structure."""

    model_config = ConfigDict(extra="ignore", frozen=True)

    # Core identification fields
    event_id: Optional[int] = Field(None, validation_alias="eventId")
    transaction_time: Optional[str] = Field(None, validation_alias="transactionTime")
    change_type: Optional[str] = Field(None, validation_alias="changeType")
    network: str = Field(..., serialization_alias="network")
    unique_id: str = Field(
        ..., validation_alias="uniqueId", serialization_alias="material_id"
    )
    traffic_code: Optional[str] = Field(
        None,
        validation_alias="trafficCode",
        serialization_alias="episode_production_number",
    )

    # Platform and provider information
    platform_name: Optional[str] = Field(None, validation_alias="platformName")
    provider: Optional[str] = Field(None, validation_alias="provider")
    provider_id: Optional[str] = Field(None, validation_alias="providerId")

    # Program identification fields
    show_title: str = Field(
        ..., validation_alias="showTitle", serialization_alias="content_name"
    )
    season_number: Optional[int] = Field(
        None, validation_alias="seasonNumber", serialization_alias="season_num"
    )
    episode_number: Optional[int] = Field(
        None, validation_alias="episodeNumber", serialization_alias="episode_number"
    )
    episode_id: Optional[str] = Field(None, validation_alias="episodeID")
    episode_name: Optional[str] = Field(
        None, validation_alias="episodeName", serialization_alias="episode_title"
    )

    # Program type and classification
    show_type: str = Field(
        ..., validation_alias="showType", serialization_alias="program_type"
    )
    asset_type: Optional[str] = Field(
        None, validation_alias="assetType", serialization_alias="episode_category"
    )

    # Radar and external identifiers
    radar_product_id: Optional[str] = Field(
        None, validation_alias="radarProductId", serialization_alias="radar_group_id"
    )

    # VOD window information
    vod4_window_start: Optional[str] = Field(None, validation_alias="vod4WindowStart")
    vod4_window_end: Optional[str] = Field(None, validation_alias="vod4WindowEnd")
    vod8_window_start: Optional[str] = Field(None, validation_alias="vod8WindowStart")
    vod8_window_end: Optional[str] = Field(None, validation_alias="vod8WindowEnd")
    c3_window_start: Optional[str] = Field(None, validation_alias="c3WindowStart")
    c3_window_end: Optional[str] = Field(None, validation_alias="c3WindowEnd")
    c7_window_start: Optional[str] = Field(None, validation_alias="c7WindowStart")
    c7_window_end: Optional[str] = Field(None, validation_alias="c7WindowEnd")
    clean_start: Optional[str] = Field(None, validation_alias="cleanStart")
    clean_end: Optional[str] = Field(None, validation_alias="cleanEnd")

    # VOD titles and descriptions
    vod_title: Optional[str] = Field(None, validation_alias="vodTitle")
    vod_short_title: Optional[str] = Field(
        None, validation_alias="vodShortTitle", serialization_alias="title_brief"
    )
    episode_vod_title: Optional[str] = Field(None, validation_alias="episodeVodTitle")
    episode_vod_short_title: Optional[str] = Field(None, validation_alias="episodeVodShortTitle")

    # Content metadata and descriptions
    summary_short: Optional[str] = Field(
        None,
        validation_alias="summaryShort",
        serialization_alias="episode_short_synopsis",
    )
    one_line_description: Optional[str] = Field(
        None,
        validation_alias="oneLineDescription",
        serialization_alias="episode_long_synopsis",
    )

    # Ratings
    rating: Optional[str] = Field(None, serialization_alias="default_rating")
    episode_rating: Optional[str] = Field(None, validation_alias="episodeRating")
    movie_rating: Optional[str] = Field(None, validation_alias="movieRating")

    # Technical details
    display_run_time: Optional[str] = Field(None, validation_alias="displayRunTime")
    closed_captioning: Optional[str] = Field(None, validation_alias="closedCaptioning")
    hd_content: Optional[Union[str, bool]] = Field(None, validation_alias="hdContent")
    audio_type: Optional[str] = Field(None, validation_alias="audioType")
    languages: Optional[str] = Field(None)
    trick_modes_restricted: Optional[str] = Field(None, validation_alias="trickModesRestricted")

    # Dates and years
    year: Optional[int] = Field(None, serialization_alias="content_start_year")
    air_date: Optional[str] = Field(
        None, validation_alias="airDate", serialization_alias="episode_airdate"
    )
    create_date: Optional[str] = Field(None, validation_alias="createDate")
    original_airdate: Optional[str] = Field(None, validation_alias="originalAirdate")
    episode_original_air_date: Optional[bool] = Field(None, validation_alias="episodeOriginalAirDate")

    # Licensing windows
    licensing_window_start: Optional[str] = Field(
        None,
        validation_alias="licensingWindowStart",
        serialization_alias="episode_window_start",
    )
    licensing_window_end: Optional[str] = Field(
        None,
        validation_alias="licensingWindowEnd",
        serialization_alias="episode_window_end",
    )

    # Genre and categorization
    genres: Optional[str] = Field(None, serialization_alias="genre_code")
    actors: Optional[str] = Field(None, serialization_alias="actors_raw")
    cmc_categories: Optional[List[str]] = Field(
        None, validation_alias="cmcCategories", serialization_alias="hd_mappings"
    )

    # Schedule and materials
    schedule_materials: Optional[str] = Field(None, validation_alias="scheduleMaterials")

    # File information
    video_file_name: Optional[str] = Field(
        None, validation_alias="videoFileName", serialization_alias="source_filename"
    )
    xml_file_name: Optional[str] = Field(None, validation_alias="xmlFileName")
    published_date_time: Optional[str] = Field(None, validation_alias="publishedDateTime")
    s3_file_location: Optional[str] = Field(None, validation_alias="s3FileLocation")

    # Asset information
    asset_id_package: Optional[str] = Field(None, validation_alias="assetIdPackage")
    asset_name_package: Optional[str] = Field(None, validation_alias="assetNamePackage")
    asset_id_title: Optional[str] = Field(None, validation_alias="assetIdTitle")
    asset_name_title: Optional[str] = Field(None, validation_alias="assetNameTitle")
    asset_id_movie: Optional[str] = Field(None, validation_alias="assetIdMovie")
    asset_name_movie: Optional[str] = Field(None, validation_alias="assetNameMovie")
    asset_id_post: Optional[str] = Field(None, validation_alias="assetIdPost")
    asset_name_post: Optional[str] = Field(None, validation_alias="assetNamePost")

    # Series information
    series_id: Optional[str] = Field(None, validation_alias="seriesId")
    series_name: Optional[str] = Field(None, validation_alias="seriesName")

    # Descriptions
    description_package: Optional[str] = Field(None, validation_alias="descriptionPackage")
    description_title: Optional[str] = Field(None, validation_alias="descriptionTitle")
    description_movie: Optional[str] = Field(None, validation_alias="descriptionMovie")
    description_post: Optional[str] = Field(None, validation_alias="descriptionPost")

    # Additional metadata
    product: Optional[str] = Field(None, validation_alias="product")
    billing_id: Optional[str] = Field(None, validation_alias="billingID")
    provider_qa_contact: Optional[str] = Field(None, validation_alias="providerQaContact")
    content_file_size_image: Optional[int] = Field(None, validation_alias="contentFileSizeImage")
    content_check_sum_image: Optional[str] = Field(None, validation_alias="contentCheckSumImage")

    # Season and episode flags
    season_premiere: Optional[str] = Field(None, validation_alias="seasonPremiere")
    season_finale: Optional[str] = Field(None, validation_alias="seasonFinale")
    title_sort_name: Optional[str] = Field(None, validation_alias="titleSortName")
    country_of_origin: Optional[str] = Field(None, validation_alias="countryOfOrigin")
    authority: Optional[str] = Field(None, validation_alias="authority")
    content_rating_descriptors: Optional[str] = Field(None, validation_alias="contentRatingDescriptors")
    show_code: Optional[str] = Field(None, validation_alias="showCode")
    placing_id: Optional[int] = Field(None, validation_alias="placingId")

    # Artwork information
    artworks_url: Optional[str] = Field(None, validation_alias="artworksUrl")
    artworks_type: Optional[str] = Field(None, validation_alias="artworksType")
    artworks_file_name: Optional[str] = Field(None, validation_alias="artworksFileName")

    @field_validator("show_type", mode="before")
    @classmethod
    def validate_show_type(cls, v: str) -> str:
        """Transform showType values to match expected program_type values."""
        if isinstance(v, str):
            if v.lower() == "tvshow":
                return "series"
            elif v.lower() in ["movie", "special"]:
                return v.lower()
        return v or "series"

    @field_validator("hd_content", mode="before")
    @classmethod
    def validate_hd_content(cls, v: Union[str, bool, None]) -> Optional[bool]:
        """Transform hdContent to boolean."""
        if isinstance(v, str):
            return v.upper() == "Y"
        return v

    @field_validator("air_date", "create_date", "original_airdate", "licensing_window_start", "licensing_window_end", mode="before")
    @classmethod
    def validate_datetime_fields(cls, v: Optional[str]) -> Optional[str]:
        """Validate and normalize datetime fields."""
        if not v or v == "null":
            return None

        # Handle ISO 8601 format with Z suffix
        if isinstance(v, str) and v.endswith('Z'):
            try:
                # Validate the datetime format
                datetime.fromisoformat(v.replace('Z', '+00:00'))
                return v
            except ValueError:
                logger_service.warning(f"Invalid datetime format: {v}")
                return None
        return v

    @field_validator("episode_number", "season_number", mode="before")
    @classmethod
    def validate_numeric_fields(cls, v: Union[str, int, None]) -> Optional[int]:
        """Validate and convert numeric fields."""
        if v is None or v == "":
            return None
        if isinstance(v, str):
            try:
                return int(v)
            except ValueError:
                logger_service.warning(f"Invalid numeric value: {v}")
                return None
        return v

    @field_validator("cmc_categories", mode="before")
    @classmethod
    def validate_cmc_categories(cls, v: Union[List[str], str, None]) -> Optional[List[str]]:
        """Validate and normalize CMC categories."""
        if not v:
            return None
        if isinstance(v, str):
            # Handle single string as list
            return [v]
        return v

    @model_validator(mode="before")
    @classmethod
    def set_defaults_and_normalize(cls, values: Dict[str, Any]) -> Dict[str, Any]:
        """Set default values and normalize data for required RASCL fields."""
        # Set default country
        if "country" not in values:
            values["country"] = "USA"

        # Set default content type
        if "content_type" not in values:
            values["content_type"] = "Long Form"

        # Set default C-Type and D-Type values
        if "episode_c_type" not in values:
            values["episode_c_type"] = "C3"

        if "episode_d_type" not in values:
            values["episode_d_type"] = "D4"

        # Handle episodeID field mapping to episode_number if needed
        if "episodeID" in values and "episodeNumber" not in values:
            episode_id = values.get("episodeID")
            if episode_id and str(episode_id).isdigit():
                values["episodeNumber"] = int(episode_id)

        return values

    @model_validator(mode="after")
    def log_field_issues(self) -> "BebanjoPayloadSchema":
        """Log warnings for fields that are blank, empty, or exceed length limits."""
        # Define fields that should not be empty for proper processing
        critical_fields = ["network", "show_title", "unique_id", "show_type"]

        for field_name, field_value in self:
            if field_name in critical_fields and (field_value is None or (isinstance(field_value, str) and not field_value.strip())):
                logger_service.error(
                    f"Bebanjo Validation Error: Critical field '{field_name}' is blank or empty."
                )
            elif isinstance(field_value, str):
                if not field_value.strip():
                    logger_service.warning(
                        f"Bebanjo Validation Warning: Field '{field_name}' is blank or empty."
                    )
                elif len(field_value) > 255:  # Increased limit for modern systems
                    logger_service.warning(
                        f"Bebanjo Validation Warning: Field '{field_name}' has more than 255 characters "
                        f"(length: {len(field_value)}). Value: '{field_value[:50]}...'"
                    )
        return self

    def _set_adi_defaults(
        self, adi_json: Dict[str, Dict[str, Any]], dump: Dict[str, Any]
    ) -> None:
        """Set default values for fields required by ADI JSON."""
        default_fields = {
            "episode_tms_id": "",
            "tms_id": "",
            "content_prefix": dump.get("show_code", ""),
            "comscore_c6": "",
            "copyright": dump.get("network", ""),
            "rating_content_labels": "",
            "country": "USA",
            "content_type": "Long Form",
            "episode_c_type": "C3",
            "episode_d_type": "D4",
            "cms_folder_name": "",
            "show_folder": "",
            "sd_mappings": "",
            "ad_content_id": "",
            "movie_asset_id": dump.get("asset_id_movie", ""),
            "categories_and_distribution": "",
            "reach_genre": "",
            "content_short_synopsis": "",
            "content_long_synopsis": "",
            "season_short_synopsis": "",
            "season_long_synopsis": "",
            "keywords": "",
            "bankable_date": "",
            "is_acquired": False,
            "original_content_20cf": False,
            "fmx_retro": False,
            "is_4k": self._convert_hd_content_to_4k(dump.get("hd_content")),
        }
        for field, default_value in default_fields.items():
            adi_json.setdefault(field, {"value": default_value})

    def _convert_hd_content_to_4k(self, hd_content: Union[str, bool, None]) -> bool:
        """Convert hdContent field to is_4k boolean."""
        if isinstance(hd_content, str):
            return hd_content.upper() == "Y"
        elif isinstance(hd_content, bool):
            return hd_content
        return False

    def _process_actors(self, adi_json: Dict[str, Dict[str, Any]]) -> None:
        """Parse actor string and convert to structured list."""
        if "actors_raw" not in adi_json:
            return

        actors_string = adi_json.pop("actors_raw")["value"]
        if not actors_string:
            return

        actors_list = []
        # Assumes a flat list of "Last, First, Last, First, ..."
        name_parts = [p.strip() for p in actors_string.split(",") if p.strip()]

        if len(name_parts) > 1 and len(name_parts) % 2 == 0:
            for i in range(0, len(name_parts), 2):
                actors_list.append(
                    {"first": name_parts[i + 1], "last": name_parts[i], "character": ""}
                )
        else:  # Fallback to original logic for other formats
            for actor in actors_string.split(", "):
                if not actor.strip():
                    continue
                parts = [p.strip() for p in actor.strip().split(", ")]
                if len(parts) >= 2:
                    actors_list.append(
                        {"first": parts[1], "last": parts[0], "character": ""}
                    )
                else:
                    full_name = actor.strip().split(" ")
                    if len(full_name) >= 2:
                        actors_list.append(
                            {
                                "first": " ".join(full_name[:-1]),
                                "last": full_name[-1],
                                "character": "",
                            }
                        )
        if actors_list:
            adi_json["actors"] = {"value": actors_list}

    def _process_synopsis(
        self, adi_json: Dict[str, Dict[str, Any]], dump: Dict[str, Any]
    ) -> None:
        """Set default synopsis values if they are not present."""
        # Use episode synopsis for content synopsis if not available
        if not adi_json.get("content_short_synopsis", {}).get("value"):
            episode_synopsis = adi_json.get("episode_short_synopsis", {}).get("value", "")
            if episode_synopsis:
                adi_json["content_short_synopsis"]["value"] = episode_synopsis
            else:
                adi_json["content_short_synopsis"]["value"] = f"Presented by {dump.get('network', '')}"

        if not adi_json.get("content_long_synopsis", {}).get("value"):
            episode_long_synopsis = adi_json.get("episode_long_synopsis", {}).get("value", "")
            if episode_long_synopsis:
                adi_json["content_long_synopsis"]["value"] = episode_long_synopsis
            else:
                adi_json["content_long_synopsis"]["value"] = f"Presented by {dump.get('network', '')}"

    def _map_reach_genre(self, adi_json: Dict[str, Dict[str, Any]]) -> None:
        """Map genre_code to a valid Reach genre."""
        genre_code = adi_json.get("genre_code", {}).get("value", "")
        if not genre_code or adi_json.get("reach_genre", {}).get("value"):
            return

        genre_mapping = {
            "entertainment": "Drama",
            "comedy": "Comedy",
            "drama": "Drama",
            "documentary": "Documentary",
            "news": "News",
            "sports": "Sports",
            "reality": "Reality and Game Show",
            "animation": "Animation",
            "kids": "Kids and Family",
            "family": "Kids and Family",
            "music": "Music",
            "horror": "Horror",
            "talk": "Talk Show",
        }

        reach_genre = "Drama"  # Default
        for key, value in genre_mapping.items():
            if key.lower() in genre_code.lower():
                reach_genre = value
                break
        adi_json["reach_genre"]["value"] = reach_genre

    def _process_additional_mappings(
        self, adi_json: Dict[str, Dict[str, Any]], dump: Dict[str, Any]
    ) -> None:
        """Process additional field mappings specific to Bebanjo payload."""
        # Map provider information
        if dump.get("provider"):
            adi_json["provider"] = {"value": dump["provider"]}

        # Map asset information
        asset_mappings = {
            "asset_id_package": "package_asset_id",
            "asset_name_package": "package_asset_name",
            "asset_id_title": "title_asset_id",
            "asset_name_title": "title_asset_name",
            "asset_id_post": "poster_asset_id",
            "asset_name_post": "poster_asset_name",
        }

        for bebanjo_field, adi_field in asset_mappings.items():
            if dump.get(bebanjo_field):
                adi_json[adi_field] = {"value": dump[bebanjo_field]}

        # Map technical details
        if dump.get("audio_type"):
            adi_json["audio_type"] = {"value": dump["audio_type"]}

        if dump.get("languages"):
            adi_json["languages"] = {"value": dump["languages"]}

        # Map artwork information
        if dump.get("artworks_url"):
            adi_json["artwork_url"] = {"value": dump["artworks_url"]}

        # Map billing and provider contact
        if dump.get("billing_id"):
            adi_json["billing_id"] = {"value": dump["billing_id"]}

        if dump.get("provider_qa_contact"):
            adi_json["provider_qa_contact"] = {"value": dump["provider_qa_contact"]}

    def to_adi_json(self) -> Dict[str, Dict[str, Any]]:
        """
        Convert the validated Bebanjo payload to adi_json format.

        Returns:
            Dictionary with RASCL adi_json structure where each field
            has a 'value' key containing the actual value.
        """
        dump = self.model_dump(by_alias=True, exclude_none=True)
        adi_json = {key: {"value": value} for key, value in dump.items()}

        # Apply all processing steps
        self._set_adi_defaults(adi_json, dump)
        self._process_actors(adi_json)
        self._process_synopsis(adi_json, dump)
        self._map_reach_genre(adi_json)
        self._process_additional_mappings(adi_json, dump)

        return adi_json
