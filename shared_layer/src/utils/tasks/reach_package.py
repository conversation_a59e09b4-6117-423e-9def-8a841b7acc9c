"""
Task functions for creating reach packages from ADI JSON format.
Implements the business rules from REACH_ADI_JSON_BUSINESS_RULES.md
"""

import json
from typing import Any, Dict, Optional, Union

from utils.LoggerService import logger_service
from utils.rascl.reach_series_toolbox import (
    reach_create_package,
    reach_search_for_existing_package,
    reach_search_for_series_or_special_or_movie,
    reach_update_package_given_pkg_json,
)
from utils.token_service import ReachTokenService


def create_reach_package_from_adi_json(
    prev: Dict[str, Any],
    reach_engine_login: str,
    reach_engine_password: str,
    reach_engine_address: str,
    bolt_config: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create or update a reach package from ADI JSON format using RASCL toolbox.
    
    Args:
        prev: ADI JSON data as dict from previous TaskTransformBebanjoToAdi
        reach_engine_login: Reach Engine username
        reach_engine_password: Reach Engine password
        reach_engine_address: Reach Engine server address
        bolt_config: Optional Bolt API configuration for cast/crew data
        
    Returns:
        Dict containing the created or updated reach package JSON
        
    Raises:
        ValueError: If ADI JSON is invalid
        ConnectionError: If Reach Engine operations fail
        Exception: If package search or creation fails
    """
    adi_json = prev

    # Prepare Reach Engine FQDN
    if not reach_engine_address.startswith(("http://", "https://")):
        reach_fqdn = f"https://{reach_engine_address}"
    else:
        reach_fqdn = reach_engine_address

    # Login to Reach Engine using token service
    logger_service.info("Authenticating with Reach Engine at %s", reach_fqdn)
    try:
        token_service = ReachTokenService()
        token = token_service.create_token()
        reach_token = {"Authorization": f"Bearer {token}"}
        existing_pkgs = reach_search_for_existing_package(
            adi_json, reach_token, reach_fqdn
        )
        if isinstance(existing_pkgs, list) and len(existing_pkgs) == 1 and isinstance(existing_pkgs[0], dict):
            pkg_json = existing_pkgs[0]
            logger_service.info(
                "Updating existing Reach package with ID %s", pkg_json.get("id")
            )
            updated_pkg = reach_update_package_given_pkg_json(
                pkg_json, reach_token, reach_fqdn
            )
            return updated_pkg

        logger_service.info("Creating new Reach package from ADI JSON")
        new_pkg = reach_create_package(adi_json, reach_token, reach_fqdn)
        return new_pkg
    except Exception as e:
        error_msg = f"Error processing Reach package from ADI JSON: {e}"
        logger_service.error(error_msg)
        raise Exception(error_msg)


def _prepare_rascl_config(bolt_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare configuration dictionary for RASCL toolbox.
    
    Args:
        bolt_config: Bolt API configuration
        
    Returns:
        Configuration dictionary for RASCL toolbox
    """
    return {
        "bolt_bearer_token": bolt_config.get("bearer_token", ""),
        "auth_z_client_id": bolt_config.get("client_id", ""),
        "auth_z_client_secret": bolt_config.get("client_secret", ""),
        "auth_z_grant_type": bolt_config.get("grant_type", "client_credentials"),
        "auth_z_scope": bolt_config.get("scope", ""),
        "gracenote_api_key": bolt_config.get("gracenote_api_key", ""),
    }
