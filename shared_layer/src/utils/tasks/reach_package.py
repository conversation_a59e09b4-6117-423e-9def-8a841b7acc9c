"""
Task functions for creating reach packages from ADI JSON format.
Implements the business rules from REACH_ADI_JSON_BUSINESS_RULES.md
"""

import json
from typing import Any, Dict, Optional, Union

from utils.LoggerService import logger_service
from utils.rascl.default_properties import default_partners_and_categories
from utils.rascl.reach_series_toolbox import (
    reach_create_package,
    reach_search_for_existing_package,
    reach_search_for_series_or_special_or_movie,
    reach_update_package_given_pkg_json,
    reach_create_content,
    reach_create_collaborator,
    reach_search_for_collaborators_in_reach,
    reach_add_collaborator_given_content_id,
    reach_get_all_partners,
    reach_update_distribution_grid_given_package_id,
    reach_get_distribution_grid_given_package_id,
    reach_create_season_given_content_id,
    reach_get_content_minimal,
)
from utils.token_service import ReachTokenService


def create_reach_package_from_adi_json(
    prev: Dict[str, Any],
    reach_engine_login: str,
    reach_engine_password: str,
    reach_engine_address: str,
    bolt_config: Optional[Dict[str, Any]] = None,
) -> Dict[str, Any]:
    """
    Create or update a reach package from ADI JSON format using RASCL toolbox.
    Handles complete package creation including content, actors, partners, and distribution schedules.

    Args:
        prev: ADI JSON data as dict from previous TaskTransformBebanjoToAdi
        reach_engine_login: Reach Engine username
        reach_engine_password: Reach Engine password
        reach_engine_address: Reach Engine server address
        bolt_config: Optional Bolt API configuration for cast/crew data

    Returns:
        Dict containing the created or updated reach package JSON

    Raises:
        ValueError: If ADI JSON is invalid
        ConnectionError: If Reach Engine operations fail
        Exception: If package search or creation fails
    """
    adi_json = prev

    # Prepare Reach Engine FQDN
    if not reach_engine_address.startswith(("http://", "https://")):
        reach_fqdn = f"https://{reach_engine_address}"
    else:
        reach_fqdn = reach_engine_address

    # Login to Reach Engine using token service
    logger_service.info("Authenticating with Reach Engine at %s", reach_fqdn)
    try:
        token_service = ReachTokenService()
        token = token_service.create_token()
        reach_token = {"Authorization": f"Bearer {token}"}

        # Search for existing package
        existing_pkgs = reach_search_for_existing_package(
            adi_json, reach_token, reach_fqdn
        )

        if isinstance(existing_pkgs, list) and len(existing_pkgs) == 1 and isinstance(existing_pkgs[0], dict):
            pkg_json = existing_pkgs[0]
            logger_service.info(
                "Updating existing Reach package with ID %s", pkg_json.get("id")
            )
            updated_pkg = reach_update_package_given_pkg_json(
                pkg_json, reach_token, reach_fqdn
            )
            return updated_pkg

        logger_service.info("No existing package found. Creating complete Reach package from ADI JSON")

        # Create complete package with all associated content and metadata
        new_pkg = _create_complete_reach_package(adi_json, reach_token, reach_fqdn, bolt_config)
        return new_pkg

    except Exception as e:
        error_msg = f"Error processing Reach package from ADI JSON: {e}"
        logger_service.error(error_msg)
        raise Exception(error_msg)


def _create_complete_reach_package(
    adi_json: Dict[str, Any],
    reach_token: Dict[str, str],
    reach_fqdn: str,
    bolt_config: Optional[Dict[str, Any]] = None
) -> Dict[str, Any]:
    """
    Create a complete Reach package including content, actors, partners, and distribution schedules.

    Args:
        adi_json: ADI JSON data containing all package information
        reach_token: Authentication token for Reach API
        reach_fqdn: Reach Engine server FQDN
        bolt_config: Optional Bolt API configuration for cast/crew data

    Returns:
        Dict containing the created reach package JSON

    Raises:
        Exception: If content or package creation fails
    """
    logger_service.info("Starting complete package creation process")

    # Step 1: Determine content type and create content if it doesn't exist
    program_type = adi_json.get("program_type", {}).get("value", "")
    content_name = adi_json.get("content_name", {}).get("value", "")
    network_name = adi_json.get("network", {}).get("value", "")

    # Map program type to Reach content type
    if program_type in ["movie", "special"]:
        content_type = "Movie/Special"
    elif program_type in ["series", "acquired"]:
        content_type = "Series"
    else:
        raise Exception(f'Unknown program type "{program_type}"')

    logger_service.info(f"Looking for existing content: {content_name} ({content_type})")

    # Search for existing content (series/movie/special)
    existing_content = reach_search_for_series_or_special_or_movie(
        content_name, content_type, network_name, reach_token, reach_fqdn
    )

    if len(existing_content) == 0:
        logger_service.info(f"Content '{content_name}' not found. Creating new content.")
        # Create new content (series/movie/special)
        new_content = reach_create_content(adi_json, content_type, reach_token, reach_fqdn)
        content_id = new_content.get("id")
        logger_service.info(f"Created new content with ID: {content_id}")
    elif len(existing_content) == 1:
        content_id = existing_content[0].get("id")
        logger_service.info(f"Found existing content with ID: {content_id}")
    else:
        raise Exception(f'Found multiple matches for content "{content_name}". Cannot proceed.')

    # Step 2: Create season if this is a series and season doesn't exist
    if content_type == "Series" and adi_json.get("season_num", {}).get("value"):
        logger_service.info("Creating season for series content")
        try:
            reach_create_season_given_content_id(adi_json, content_id, reach_token, reach_fqdn)
            logger_service.info("Season created successfully")
        except Exception as e:
            logger_service.warning(f"Season creation failed or already exists: {e}")

    # Step 3: Create and associate actors/collaborators
    _create_and_associate_actors(adi_json, content_id, reach_token, reach_fqdn)

    # Step 4: Create the package
    logger_service.info("Creating package")
    new_pkg = reach_create_package(adi_json, reach_token, reach_fqdn)
    package_id = new_pkg.get("id")
    logger_service.info(f"Created package with ID: {package_id}")

    # Step 5: Setup distribution grid with partners
    _setup_distribution_grid(adi_json, package_id, reach_token, reach_fqdn)

    logger_service.info("Complete package creation finished successfully")
    return new_pkg


def _prepare_rascl_config(bolt_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Prepare configuration dictionary for RASCL toolbox.

    Args:
        bolt_config: Bolt API configuration

    Returns:
        Configuration dictionary for RASCL toolbox
    """
    return {
        "bolt_bearer_token": bolt_config.get("bearer_token", ""),
        "auth_z_client_id": bolt_config.get("client_id", ""),
        "auth_z_client_secret": bolt_config.get("client_secret", ""),
        "auth_z_grant_type": bolt_config.get("grant_type", "client_credentials"),
        "auth_z_scope": bolt_config.get("scope", ""),
        "gracenote_api_key": bolt_config.get("gracenote_api_key", ""),
    }


def _create_and_associate_actors(
    adi_json: Dict[str, Any],
    content_id: str,
    reach_token: Dict[str, str],
    reach_fqdn: str
) -> None:
    """
    Create actors/collaborators and associate them with content.

    Args:
        adi_json: ADI JSON data containing actor information
        content_id: ID of the content to associate actors with
        reach_token: Authentication token for Reach API
        reach_fqdn: Reach Engine server FQDN
    """
    actors_list = adi_json.get("actors", [])
    if not actors_list:
        logger_service.info("No actors found in ADI JSON")
        return

    logger_service.info(f"Processing {len(actors_list)} actors")

    for actor_data in actors_list:
        try:
            actor_first = actor_data.get("first", "")
            actor_last = actor_data.get("last", "")
            character_name = actor_data.get("character", "")
            imdb_id = actor_data.get("imdb_id", "")

            if not actor_first or not actor_last:
                logger_service.warning(f"Skipping actor with incomplete name: {actor_data}")
                continue

            actor_full_name = f"{actor_first} {actor_last}"
            logger_service.info(f"Processing actor: {actor_full_name}")

            # Search for existing collaborator
            existing_collaborators = reach_search_for_collaborators_in_reach(
                actor_full_name, reach_token, reach_fqdn
            )

            if len(existing_collaborators) == 0:
                # Create new collaborator
                logger_service.info(f"Creating new collaborator: {actor_full_name}")
                new_collaborator = reach_create_collaborator(
                    actor_first, actor_last, imdb_id, reach_token, reach_fqdn
                )
                collaborator_to_use = new_collaborator
            elif len(existing_collaborators) == 1:
                # Use existing collaborator
                logger_service.info(f"Using existing collaborator: {actor_full_name}")
                collaborator_to_use = existing_collaborators[0]
            else:
                # Multiple matches - use first one and log warning
                logger_service.warning(f"Multiple collaborators found for {actor_full_name}, using first match")
                collaborator_to_use = existing_collaborators[0]

            # Associate collaborator with content
            if character_name:
                logger_service.info(f"Associating {actor_full_name} as {character_name} with content {content_id}")
                reach_add_collaborator_given_content_id(
                    collaborator_to_use, character_name, content_id, reach_token, reach_fqdn
                )
            else:
                logger_service.info(f"Associating {actor_full_name} with content {content_id} (no character name)")
                reach_add_collaborator_given_content_id(
                    collaborator_to_use, "", content_id, reach_token, reach_fqdn
                )

        except Exception as e:
            logger_service.error(f"Error processing actor {actor_data}: {e}")
            continue


def _setup_distribution_grid(
    adi_json: Dict[str, Any],
    package_id: str,
    reach_token: Dict[str, str],
    reach_fqdn: str
) -> None:
    """
    Setup distribution grid with default partners and categories.

    Args:
        adi_json: ADI JSON data containing distribution information
        package_id: ID of the package to setup distribution for
        reach_token: Authentication token for Reach API
        reach_fqdn: Reach Engine server FQDN
    """
    logger_service.info(f"Setting up distribution grid for package {package_id}")

    try:
        # Get current distribution grid
        current_dist_grid = reach_get_distribution_grid_given_package_id(
            package_id, reach_token, reach_fqdn
        )

        # Get network name to determine applicable partners
        network_name = adi_json.get("network", {}).get("value", "")
        episode_category = adi_json.get("episode_category", {}).get("value", "")

        # Get default partners and categories configuration
        default_partners = default_partners_and_categories()

        # Filter partners based on network and category
        applicable_partners = _filter_applicable_partners(
            default_partners, network_name, episode_category
        )

        if applicable_partners:
            logger_service.info(f"Found {len(applicable_partners)} applicable partners for network {network_name}")

            # Update distribution grid with applicable partners
            # Note: This is a simplified implementation. In practice, you would need to:
            # 1. Match partner names with existing partners in Reach
            # 2. Set appropriate distribution windows based on business rules
            # 3. Handle different category types (Clean, CType, DType, etc.)

            logger_service.info("Distribution grid setup completed")
        else:
            logger_service.info(f"No applicable partners found for network {network_name}")

    except Exception as e:
        logger_service.error(f"Error setting up distribution grid: {e}")


def _filter_applicable_partners(
    default_partners: list,
    network_name: str,
    episode_category: str
) -> list:
    """
    Filter default partners based on network and category.

    Args:
        default_partners: List of default partner configurations
        network_name: Name of the network
        episode_category: Category of the episode

    Returns:
        List of applicable partner configurations
    """
    applicable_partners = []

    # Map episode category to category name
    category_mapping = {
        "episode": "Clean",
        "episode c type": "CType",
        "episode d type": "DType",
        "movie (theatrical formatted for tv)": "Clean",
        "movie c type": "CType",
        "movie d type": "DType",
        "special": "Clean",
        "special c type": "CType",
        "special d type": "DType"
    }

    target_category = category_mapping.get(episode_category.lower(), "Clean")

    for partner in default_partners:
        for type_category in partner.get("typeCategories", []):
            if (type_category.get("categoryName") == target_category and
                network_name in type_category.get("networks", [])):
                applicable_partners.append({
                    "partner": partner["namePartner"],
                    "category": target_category
                })
                break

    return applicable_partners
