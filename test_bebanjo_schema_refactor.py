#!/usr/bin/env python3
"""
Test script for the refactored BebanjoPayloadSchema.
This script validates the schema against the provided Bebanjo JSON payload sample.
"""

import json
import sys
from pathlib import Path

# Add the shared_layer to the path
sys.path.insert(0, str(Path(__file__).parent / "shared_layer" / "src"))

from pydantic import ValidationError
from utils.schemas.payloads import BebanjoPayloadSchema

# Sample Bebanjo JSON payload from the user's request
BEBANJO_SAMPLE_PAYLOAD = {
    "eventId": 0,
    "transactionTime": None,
    "vod4WindowStart": None,
    "vod4WindowEnd": None,
    "vod8WindowStart": None,
    "vod8WindowEnd": None,
    "c3WindowStart": None,
    "c3WindowEnd": None,
    "c7WindowStart": None,
    "c7WindowEnd": None,
    "cleanStart": None,
    "cleanEnd": None,
    "changeType": "A",
    "network": "DC",
    "uniqueId": "mass0000000021708936",
    "trafficCode": "K100014340",
    "platformName": "VOD FVOD DC HD LF",
    "provider": "DISNEY_CHANNEL_HD",
    "vodTitle": "MLBUG2-02_HD_D_08-01_title",
    "vodShortTitle": "Ladybug_S02_E02_HD",
    "episodeVodTitle": None,
    "episodeVodShortTitle": "Prime Queen",
    "episodeName": "Prime Queen",
    "episodeID": "2",
    "summaryShort": "Nadia Chamack is akumatized into Prime Queen. Armed with her smartwatch, she forces Ladybug and Cat Noir to say they're in love on live TV!",
    "rating": "TV-Y7",
    "episodeRating": "",
    "movieRating": None,
    "displayRunTime": "00:30:00",
    "year": 2017,
    "cmcCategories": [
        "Disney Channel HD/Mrcls Ladybug"
    ],
    "closedCaptioning": "Y",
    "radarProductId": "182416",
    "genres": "Family",
    "actors": "",
    "licensingWindowStart": "2025-08-01T07:00:00Z",
    "licensingWindowEnd": "2025-09-01T06:59:00Z",
    "episodeOriginalAirDate": True,
    "scheduleMaterials": "[ {\n  \"tns:AssetName\" : \"DEPP2963\",\n  \"tns:PlaylistPosition\" : 1,\n  \"tns:SegmentNumber\" : null\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 2,\n  \"tns:SegmentNumber\" : 1\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 3,\n  \"tns:SegmentNumber\" : 2\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 4,\n  \"tns:SegmentNumber\" : 3\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 5,\n  \"tns:SegmentNumber\" : 4\n}, {\n  \"tns:AssetName\" : \"K100014340\",\n  \"tns:PlaylistPosition\" : 6,\n  \"tns:SegmentNumber\" : 5\n} ]",
    "showTitle": "Miraculous: Tales of Ladybug & Cat Noir",
    "seasonNumber": 2,
    "showType": "TVShow",
    "episodeNumber": 2,
    "airDate": "2025-08-01T07:00:00Z",
    "createDate": "2025-08-01T07:00:00Z",
    "oneLineDescription": "It's lights, camera, action for Ladybug and Cat Noir!",
    "originalAirdate": "2017-10-19T07:00:00Z",
    "artworksUrl": "https://movida-staging.s3.amazonaws.com/images/attachments/2227862/original/1555008480-4409b1f68a35ad823d0e423226413d29b3b0144b.bmp",
    "artworksType": "poster",
    "artworksFileName": "DC_MIR_STB_320x240_14059192.bmp",
    "assetType": "VOD",
    "assetName": "MLBUG2-02_HD_D_08-01",
    "xmlFileName": "DIS_ADC0000000021708936V.xml",
    "videoFileName": "DIS_ADC0000000021708936V.mpg",
    "publishedDateTime": None,
    "s3FileLocation": "mds-bebanjo-schedules-dev/Kids/2025/5/VOD_FVOD_DC_HD_LF-mass0000000021708936-20250527T144923495.xml",
    "assetIdPackage": "pack0000000021708936",
    "assetNamePackage": "MLBUG2-02_HD_D_08-01",
    "assetIdTitle": "tass0000000021708936",
    "assetNameTitle": "MLBUG2-02_HD_D_08-01_title",
    "seriesId": "",
    "seriesName": "Miraculous Ladybug",
    "providerId": "disneychannel.com",
    "descriptionPackage": "MLBUG2-02_HD_D_08-01 asset package",
    "descriptionTitle": "MLBUG2-02_HD_D_08-01 title asset",
    "product": "DISCHD",
    "titleBrief": "Ladybug_S02_E02_HD",
    "billingID": "00000",
    "providerQaContact": "<EMAIL>",
    "assetIdMovie": "mass0000000021708936",
    "assetNameMovie": "MLBUG2-02_HD_D_08-01_movie",
    "descriptionMovie": "MLBUG2-02_HD_D_08-01 movie asset",
    "hdContent": "Y",
    "audioType": "Dolby 5.1",
    "languages": "en",
    "trickModesRestricted": "n/a",
    "assetIdPost": "post0000000021708936",
    "assetNamePost": "MLBUG2-02_HD_D_08-01_poster",
    "descriptionPost": "MLBUG2-02_HD_D_08-01 poster asset",
    "contentFileSizeImage": 230456,
    "contentCheckSumImage": "2ff6b4fce128eb85073587d3c8a8c970",
    "seasonPremiere": None,
    "seasonFinale": None,
    "titleSortName": None,
    "countryOfOrigin": None,
    "authority": None,
    "contentRatingDescriptors": "",
    "showCode": "MLBUG",
    "placingId": 263980712
}


def test_bebanjo_schema_validation():
    """Test the BebanjoPayloadSchema validation with the sample payload."""
    print("Testing BebanjoPayloadSchema validation...")
    
    try:
        # Validate the schema
        schema = BebanjoPayloadSchema(**BEBANJO_SAMPLE_PAYLOAD)
        print("✅ Schema validation successful!")
        
        # Print some key validated fields
        print(f"Network: {schema.network}")
        print(f"Show Title: {schema.show_title}")
        print(f"Show Type: {schema.show_type}")
        print(f"Season Number: {schema.season_number}")
        print(f"Episode Number: {schema.episode_number}")
        print(f"Episode Name: {schema.episode_name}")
        print(f"Unique ID: {schema.unique_id}")
        print(f"HD Content: {schema.hd_content}")
        
        return schema
        
    except ValidationError as e:
        print("❌ Schema validation failed!")
        print("Validation errors:")
        for error in e.errors():
            print(f"  - {error['loc']}: {error['msg']}")
        return None


def test_adi_json_conversion(schema: BebanjoPayloadSchema):
    """Test the conversion to adi_json format."""
    print("\nTesting adi_json conversion...")
    
    try:
        adi_json = schema.to_adi_json()
        print("✅ ADI JSON conversion successful!")
        
        # Print some key converted fields
        key_fields = [
            "network", "content_name", "program_type", "season_num", 
            "episode_number", "episode_title", "material_id", "is_4k"
        ]
        
        print("\nKey ADI JSON fields:")
        for field in key_fields:
            if field in adi_json:
                print(f"  {field}: {adi_json[field]['value']}")
        
        return adi_json
        
    except Exception as e:
        print(f"❌ ADI JSON conversion failed: {e}")
        return None


def main():
    """Main test function."""
    print("=" * 60)
    print("BEBANJO PAYLOAD SCHEMA REFACTOR TEST")
    print("=" * 60)
    
    # Test schema validation
    schema = test_bebanjo_schema_validation()
    
    if schema:
        # Test adi_json conversion
        adi_json = test_adi_json_conversion(schema)
        
        if adi_json:
            print(f"\n✅ All tests passed! ADI JSON contains {len(adi_json)} fields.")
            
            # Save the result for inspection
            with open("test_adi_json_output.json", "w") as f:
                json.dump(adi_json, f, indent=2)
            print("📄 ADI JSON output saved to 'test_adi_json_output.json'")
        else:
            print("\n❌ ADI JSON conversion test failed.")
    else:
        print("\n❌ Schema validation test failed.")
    
    print("=" * 60)


if __name__ == "__main__":
    main()
